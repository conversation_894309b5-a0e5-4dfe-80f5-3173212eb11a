﻿#pragma once

namespace AloTool
{
	class ThemoModel
	{
	private:
		double load = 0;
		double heat_coeff = 1.0;
		double ambientTemp = 25;       // 环境温度(°C)
		double thermalCapacity;   // 热容(J/K)
		double convectionCoeff;   // 对流换热系数(W/K)
		double currentTemp;       // 当前温度(°C)
	private:
		// 计算发热功率 P = V²/R
		double getHeatPower() {
			return load * load * heat_coeff;
		}

		// 计算散热功率 P_loss = h * (T - T_ambient)
		double getHeatLoss() {
			return convectionCoeff * (currentTemp - ambientTemp);
		}

		// 温度变化率 dT/dt = (P_in - P_out) / C
		double getTemperatureRate() {
			return (getHeatPower() - getHeatLoss()) / thermalCapacity;
		}

		// 计算理论平衡温度
		double getEquilibriumTemp() {
			return ambientTemp + getHeatPower() / convectionCoeff;
		}

	public:
		ThemoModel(double v, double r, double ambient = 25.0,
			double capacity = 100.0, double convection = 0.5)
			: ambientTemp(ambient),
			thermalCapacity(capacity), convectionCoeff(convection),
			currentTemp(ambient) {
		}
		double get_temp()const { return currentTemp; }
		// 单步时间积分
		void updateTemperature(double deltaTime) {
			double rate = getTemperatureRate();
			currentTemp += rate * deltaTime;
		}
		void setLoad(double v) { load; }
		void setHeadCoeff(double h) { heat_coeff = h; }
		void setConvectionCoeff(double c) { convectionCoeff = c; }
	};
}
