<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1236pt" height="908pt" viewBox="0.00 0.00 1236.28 908.00">
<g id="graph0" class="graph" transform="translate(-850.1087671539603,1469.6169384365073) scale(2.639015882602803)" data-name="DeviceTree">

<polygon fill="white" stroke="none" points="-4,4 -4,-904 1232.28,-904 1232.28,4 -4,4" style=""/>
<!-- root -->
<g id="node1" class="node" pointer-events="visible" data-name="root">

<ellipse fill="none" stroke="black" cx="537.68" cy="-882" rx="27" ry="18" style=""/>
<text text-anchor="middle" x="537.68" y="-877.8" font-family="Times,serif" font-size="14.00" style="">root</text>
</g>
<!-- power -->
<g id="node2" class="node" pointer-events="visible" data-name="power">

<ellipse fill="none" stroke="black" cx="537.68" cy="-810" rx="35.34" ry="18" style=""/>
<text text-anchor="middle" x="537.68" y="-805.8" font-family="Times,serif" font-size="14.00" style="">power</text>
</g>
<!-- root&#45;&gt;power -->
<g id="edge1" class="edge" data-name="root-&gt;power">

<path fill="none" stroke="black" d="M537.68,-863.7C537.68,-856.41 537.68,-847.73 537.68,-839.54" style=""/>
<polygon fill="black" stroke="black" points="541.18,-839.62 537.68,-829.62 534.18,-839.62 541.18,-839.62" style=""/>
</g>
<!-- breaker1 -->
<g id="node3" class="node" pointer-events="visible" data-name="breaker1">

<ellipse fill="none" stroke="black" cx="537.68" cy="-738" rx="44.63" ry="18" style=""/>
<text text-anchor="middle" x="537.68" y="-733.8" font-family="Times,serif" font-size="14.00" style="">breaker1</text>
</g>
<!-- power&#45;&gt;breaker1 -->
<g id="edge2" class="edge" data-name="power-&gt;breaker1">

<path fill="none" stroke="black" d="M537.68,-791.7C537.68,-784.41 537.68,-775.73 537.68,-767.54" style=""/>
<polygon fill="black" stroke="black" points="541.18,-767.62 537.68,-757.62 534.18,-767.62 541.18,-767.62" style=""/>
</g>
<!-- bus600vbar -->
<g id="node4" class="node" pointer-events="visible" data-name="bus600vbar">

<ellipse fill="none" stroke="black" cx="537.68" cy="-666" rx="56.19" ry="18" style=""/>
<text text-anchor="middle" x="537.68" y="-661.8" font-family="Times,serif" font-size="14.00" style="">bus600vbar</text>
</g>
<!-- breaker1&#45;&gt;bus600vbar -->
<g id="edge3" class="edge" data-name="breaker1-&gt;bus600vbar">

<path fill="none" stroke="black" d="M537.68,-719.7C537.68,-712.41 537.68,-703.73 537.68,-695.54" style=""/>
<polygon fill="black" stroke="black" points="541.18,-695.62 537.68,-685.62 534.18,-695.62 541.18,-695.62" style=""/>
</g>
<!-- transformer1 -->
<g id="node5" class="node" pointer-events="visible" data-name="transformer1">

<ellipse fill="none" stroke="black" cx="475.68" cy="-594" rx="60.82" ry="18" style=""/>
<text text-anchor="middle" x="475.68" y="-589.8" font-family="Times,serif" font-size="14.00" style="">transformer1</text>
</g>
<!-- bus600vbar&#45;&gt;transformer1 -->
<g id="edge4" class="edge" data-name="bus600vbar-&gt;transformer1">

<path fill="none" stroke="black" d="M522.99,-648.41C515.5,-639.95 506.23,-629.49 497.89,-620.08" style=""/>
<polygon fill="black" stroke="black" points="500.52,-617.77 491.27,-612.6 495.28,-622.41 500.52,-617.77" style=""/>
</g>
<!-- breaker3 -->
<g id="node17" class="node" pointer-events="visible" data-name="breaker3">

<ellipse fill="none" stroke="black" cx="639.68" cy="-594" rx="44.63" ry="18" style=""/>
<text text-anchor="middle" x="639.68" y="-589.8" font-family="Times,serif" font-size="14.00" style="">breaker3</text>
</g>
<!-- bus600vbar&#45;&gt;breaker3 -->
<g id="edge16" class="edge" data-name="bus600vbar-&gt;breaker3">

<path fill="none" stroke="black" d="M560.83,-649.12C574.94,-639.43 593.16,-626.92 608.54,-616.37" style=""/>
<polygon fill="black" stroke="black" points="610.16,-619.5 616.43,-610.96 606.2,-613.73 610.16,-619.5" style=""/>
</g>
<!-- breaker2 -->
<g id="node6" class="node" pointer-events="visible" data-name="breaker2">

<ellipse fill="none" stroke="black" cx="395.68" cy="-522" rx="44.63" ry="18" style=""/>
<text text-anchor="middle" x="395.68" y="-517.8" font-family="Times,serif" font-size="14.00" style="">breaker2</text>
</g>
<!-- transformer1&#45;&gt;breaker2 -->
<g id="edge5" class="edge" data-name="transformer1-&gt;breaker2">

<path fill="none" stroke="black" d="M456.72,-576.41C446.44,-567.41 433.57,-556.15 422.3,-546.29" style=""/>
<polygon fill="black" stroke="black" points="424.65,-543.7 414.82,-539.75 420.04,-548.96 424.65,-543.7" style=""/>
</g>
<!-- bus400vbar -->
<g id="node7" class="node" pointer-events="visible" data-name="bus400vbar">

<ellipse fill="none" stroke="black" cx="234.68" cy="-450" rx="56.19" ry="18" style=""/>
<text text-anchor="middle" x="234.68" y="-445.8" font-family="Times,serif" font-size="14.00" style="">bus400vbar</text>
</g>
<!-- breaker2&#45;&gt;bus400vbar -->
<g id="edge6" class="edge" data-name="breaker2-&gt;bus400vbar">

<path fill="none" stroke="black" d="M366.15,-508.16C341.28,-497.35 305.38,-481.74 277.37,-469.56" style=""/>
<polygon fill="black" stroke="black" points="279.02,-466.46 268.45,-465.68 276.23,-472.88 279.02,-466.46" style=""/>
</g>
<!-- motor6 -->
<g id="node8" class="node" pointer-events="visible" data-name="motor6">

<ellipse fill="none" stroke="black" cx="42.68" cy="-378" rx="38.86" ry="18" style=""/>
<text text-anchor="middle" x="42.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">motor6</text>
</g>
<!-- bus400vbar&#45;&gt;motor6 -->
<g id="edge7" class="edge" data-name="bus400vbar-&gt;motor6">

<path fill="none" stroke="black" d="M198.57,-435.83C165.73,-423.86 117.33,-406.22 83.19,-393.77" style=""/>
<polygon fill="black" stroke="black" points="84.66,-390.58 74.07,-390.44 82.26,-397.16 84.66,-390.58" style=""/>
</g>
<!-- motor3 -->
<g id="node10" class="node" pointer-events="visible" data-name="motor3">

<ellipse fill="none" stroke="black" cx="138.68" cy="-378" rx="38.86" ry="18" style=""/>
<text text-anchor="middle" x="138.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">motor3</text>
</g>
<!-- bus400vbar&#45;&gt;motor3 -->
<g id="edge9" class="edge" data-name="bus400vbar-&gt;motor3">

<path fill="none" stroke="black" d="M212.9,-433.12C199.56,-423.39 182.32,-410.82 167.81,-400.24" style=""/>
<polygon fill="black" stroke="black" points="170.11,-397.58 159.96,-394.52 165.98,-403.24 170.11,-397.58" style=""/>
</g>
<!-- motor4 -->
<g id="node11" class="node" pointer-events="visible" data-name="motor4">

<ellipse fill="none" stroke="black" cx="234.68" cy="-378" rx="38.86" ry="18" style=""/>
<text text-anchor="middle" x="234.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">motor4</text>
</g>
<!-- bus400vbar&#45;&gt;motor4 -->
<g id="edge10" class="edge" data-name="bus400vbar-&gt;motor4">

<path fill="none" stroke="black" d="M234.68,-431.7C234.68,-424.41 234.68,-415.73 234.68,-407.54" style=""/>
<polygon fill="black" stroke="black" points="238.18,-407.62 234.68,-397.62 231.18,-407.62 238.18,-407.62" style=""/>
</g>
<!-- motor5 -->
<g id="node13" class="node" pointer-events="visible" data-name="motor5">

<ellipse fill="none" stroke="black" cx="330.68" cy="-378" rx="38.86" ry="18" style=""/>
<text text-anchor="middle" x="330.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">motor5</text>
</g>
<!-- bus400vbar&#45;&gt;motor5 -->
<g id="edge12" class="edge" data-name="bus400vbar-&gt;motor5">

<path fill="none" stroke="black" d="M256.46,-433.12C269.81,-423.39 287.05,-410.82 301.55,-400.24" style=""/>
<polygon fill="black" stroke="black" points="303.38,-403.24 309.4,-394.52 299.25,-397.58 303.38,-403.24" style=""/>
</g>
<!-- blower2 -->
<g id="node9" class="node" pointer-events="visible" data-name="blower2">

<ellipse fill="none" stroke="black" cx="42.68" cy="-306" rx="42.86" ry="18" style=""/>
<text text-anchor="middle" x="42.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">blower2</text>
</g>
<!-- motor6&#45;&gt;blower2 -->
<g id="edge8" class="edge" data-name="motor6-&gt;blower2">

<path fill="none" stroke="black" d="M42.68,-359.7C42.68,-352.41 42.68,-343.73 42.68,-335.54" style=""/>
<polygon fill="black" stroke="black" points="46.18,-335.62 42.68,-325.62 39.18,-335.62 46.18,-335.62" style=""/>
</g>
<!-- blower1 -->
<g id="node12" class="node" pointer-events="visible" data-name="blower1">

<ellipse fill="none" stroke="black" cx="233.68" cy="-306" rx="42.86" ry="18" style=""/>
<text text-anchor="middle" x="233.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">blower1</text>
</g>
<!-- motor4&#45;&gt;blower1 -->
<g id="edge11" class="edge" data-name="motor4-&gt;blower1">

<path fill="none" stroke="black" d="M234.43,-359.7C234.33,-352.41 234.21,-343.73 234.09,-335.54" style=""/>
<polygon fill="black" stroke="black" points="237.59,-335.57 233.95,-325.62 230.59,-335.67 237.59,-335.57" style=""/>
</g>
<!-- pump1 -->
<g id="node14" class="node" pointer-events="visible" data-name="pump1">

<ellipse fill="none" stroke="black" cx="332.68" cy="-306" rx="37.72" ry="18" style=""/>
<text text-anchor="middle" x="332.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">pump1</text>
</g>
<!-- motor5&#45;&gt;pump1 -->
<g id="edge13" class="edge" data-name="motor5-&gt;pump1">

<path fill="none" stroke="black" d="M331.18,-359.7C331.38,-352.41 331.63,-343.73 331.87,-335.54" style=""/>
<polygon fill="black" stroke="black" points="335.36,-335.71 332.15,-325.62 328.36,-335.51 335.36,-335.71" style=""/>
</g>
<!-- cylinder1 -->
<g id="node15" class="node" pointer-events="visible" data-name="cylinder1">

<ellipse fill="none" stroke="black" cx="330.68" cy="-234" rx="47.51" ry="18" style=""/>
<text text-anchor="middle" x="330.68" y="-229.8" font-family="Times,serif" font-size="14.00" style="">cylinder1</text>
</g>
<!-- pump1&#45;&gt;cylinder1 -->
<g id="edge14" class="edge" data-name="pump1-&gt;cylinder1">

<path fill="none" stroke="black" d="M332.19,-287.7C331.98,-280.41 331.73,-271.73 331.5,-263.54" style=""/>
<polygon fill="black" stroke="black" points="335,-263.51 331.21,-253.62 328,-263.71 335,-263.51" style=""/>
</g>
<!-- cylinder2 -->
<g id="node16" class="node" pointer-events="visible" data-name="cylinder2">

<ellipse fill="none" stroke="black" cx="443.68" cy="-234" rx="47.51" ry="18" style=""/>
<text text-anchor="middle" x="443.68" y="-229.8" font-family="Times,serif" font-size="14.00" style="">cylinder2</text>
</g>
<!-- pump1&#45;&gt;cylinder2 -->
<g id="edge15" class="edge" data-name="pump1-&gt;cylinder2">

<path fill="none" stroke="black" d="M354.88,-291C370.78,-280.97 392.48,-267.29 410.45,-255.96" style=""/>
<polygon fill="black" stroke="black" points="412.12,-259.04 418.71,-250.75 408.38,-253.12 412.12,-259.04" style=""/>
</g>
<!-- rectifier1 -->
<g id="node18" class="node" pointer-events="visible" data-name="rectifier1">

<ellipse fill="none" stroke="black" cx="659.68" cy="-522" rx="46.34" ry="18" style=""/>
<text text-anchor="middle" x="659.68" y="-517.8" font-family="Times,serif" font-size="14.00" style="">rectifier1</text>
</g>
<!-- breaker3&#45;&gt;rectifier1 -->
<g id="edge17" class="edge" data-name="breaker3-&gt;rectifier1">

<path fill="none" stroke="black" d="M644.62,-575.7C646.76,-568.24 649.3,-559.32 651.69,-550.97" style=""/>
<polygon fill="black" stroke="black" points="655,-552.14 654.38,-541.56 648.27,-550.21 655,-552.14" style=""/>
</g>
<!-- busdcbar -->
<g id="node19" class="node" pointer-events="visible" data-name="busdcbar">

<ellipse fill="none" stroke="black" cx="734.68" cy="-450" rx="45.79" ry="18" style=""/>
<text text-anchor="middle" x="734.68" y="-445.8" font-family="Times,serif" font-size="14.00" style="">busdcbar</text>
</g>
<!-- rectifier1&#45;&gt;busdcbar -->
<g id="edge18" class="edge" data-name="rectifier1-&gt;busdcbar">

<path fill="none" stroke="black" d="M676.7,-505.12C686.32,-496.14 698.53,-484.74 709.26,-474.73" style=""/>
<polygon fill="black" stroke="black" points="711.44,-477.48 716.36,-468.1 706.66,-472.36 711.44,-477.48" style=""/>
</g>
<!-- dynamic_brake1 -->
<g id="node20" class="node" pointer-events="visible" data-name="dynamic_brake1">

<ellipse fill="none" stroke="black" cx="462.68" cy="-378" rx="75.28" ry="18" style=""/>
<text text-anchor="middle" x="462.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">dynamic_brake1</text>
</g>
<!-- busdcbar&#45;&gt;dynamic_brake1 -->
<g id="edge19" class="edge" data-name="busdcbar-&gt;dynamic_brake1">

<path fill="none" stroke="black" d="M697.15,-439.34C652.38,-427.82 576.95,-408.41 523.75,-394.72" style=""/>
<polygon fill="black" stroke="black" points="524.78,-391.37 514.22,-392.26 523.03,-398.15 524.78,-391.37" style=""/>
</g>
<!-- tdinv -->
<g id="node21" class="node" pointer-events="visible" data-name="tdinv">

<ellipse fill="none" stroke="black" cx="586.68" cy="-378" rx="30.78" ry="18" style=""/>
<text text-anchor="middle" x="586.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">tdinv</text>
</g>
<!-- busdcbar&#45;&gt;tdinv -->
<g id="edge20" class="edge" data-name="busdcbar-&gt;tdinv">

<path fill="none" stroke="black" d="M706.15,-435.5C681.63,-423.91 646.26,-407.18 620.36,-394.93" style=""/>
<polygon fill="black" stroke="black" points="622.16,-391.91 611.62,-390.8 619.17,-398.24 622.16,-391.91" style=""/>
</g>
<!-- mp1_inv -->
<g id="node25" class="node" pointer-events="visible" data-name="mp1_inv">

<ellipse fill="none" stroke="black" cx="680.68" cy="-378" rx="45.25" ry="18" style=""/>
<text text-anchor="middle" x="680.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">mp1_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp1_inv -->
<g id="edge24" class="edge" data-name="busdcbar-&gt;mp1_inv">

<path fill="none" stroke="black" d="M721.88,-432.41C715.43,-424.04 707.46,-413.71 700.25,-404.37" style=""/>
<polygon fill="black" stroke="black" points="703.19,-402.45 694.31,-396.67 697.65,-406.72 703.19,-402.45" style=""/>
</g>
<!-- mp2_inv -->
<g id="node28" class="node" pointer-events="visible" data-name="mp2_inv">

<ellipse fill="none" stroke="black" cx="789.68" cy="-378" rx="45.25" ry="18" style=""/>
<text text-anchor="middle" x="789.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">mp2_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp2_inv -->
<g id="edge28" class="edge" data-name="busdcbar-&gt;mp2_inv">

<path fill="none" stroke="black" d="M747.72,-432.41C754.29,-424.04 762.41,-413.71 769.75,-404.37" style=""/>
<polygon fill="black" stroke="black" points="772.38,-406.69 775.81,-396.66 766.87,-402.36 772.38,-406.69" style=""/>
</g>
<!-- mp3_inv -->
<g id="node31" class="node" pointer-events="visible" data-name="mp3_inv">

<ellipse fill="none" stroke="black" cx="904.68" cy="-378" rx="45.25" ry="18" style=""/>
<text text-anchor="middle" x="904.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">mp3_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp3_inv -->
<g id="edge32" class="edge" data-name="busdcbar-&gt;mp3_inv">

<path fill="none" stroke="black" d="M765.47,-436.32C792.91,-425.02 833.28,-408.4 863.5,-395.96" style=""/>
<polygon fill="black" stroke="black" points="864.81,-399.2 872.72,-392.16 862.15,-392.73 864.81,-399.2" style=""/>
</g>
<!-- invertor1 -->
<g id="node34" class="node" pointer-events="visible" data-name="invertor1">

<ellipse fill="none" stroke="black" cx="1025.68" cy="-378" rx="46.37" ry="18" style=""/>
<text text-anchor="middle" x="1025.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">invertor1</text>
</g>
<!-- busdcbar&#45;&gt;invertor1 -->
<g id="edge36" class="edge" data-name="busdcbar-&gt;invertor1">

<path fill="none" stroke="black" d="M773.57,-440C818.29,-429.6 893.86,-411.89 958.68,-396 964.58,-394.55 970.77,-393.01 976.9,-391.48" style=""/>
<polygon fill="black" stroke="black" points="977.49,-394.94 986.33,-389.1 975.77,-388.15 977.49,-394.94" style=""/>
</g>
<!-- invertor2 -->
<g id="node39" class="node" pointer-events="visible" data-name="invertor2">

<ellipse fill="none" stroke="black" cx="1155.68" cy="-378" rx="46.37" ry="18" style=""/>
<text text-anchor="middle" x="1155.68" y="-373.8" font-family="Times,serif" font-size="14.00" style="">invertor2</text>
</g>
<!-- busdcbar&#45;&gt;invertor2 -->
<g id="edge42" class="edge" data-name="busdcbar-&gt;invertor2">

<path fill="none" stroke="black" d="M777.73,-443.41C843.33,-434.65 972.17,-416.58 1080.68,-396 1088.8,-394.46 1097.37,-392.67 1105.71,-390.85" style=""/>
<polygon fill="black" stroke="black" points="1106.39,-394.28 1115.39,-388.68 1104.86,-387.45 1106.39,-394.28" style=""/>
</g>
<!-- tdmotor -->
<g id="node22" class="node" pointer-events="visible" data-name="tdmotor">

<ellipse fill="none" stroke="black" cx="529.68" cy="-306" rx="41.73" ry="18" style=""/>
<text text-anchor="middle" x="529.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">tdmotor</text>
</g>
<!-- tdinv&#45;&gt;tdmotor -->
<g id="edge21" class="edge" data-name="tdinv-&gt;tdmotor">

<path fill="none" stroke="black" d="M574.03,-361.46C567.07,-352.92 558.26,-342.1 550.33,-332.36" style=""/>
<polygon fill="black" stroke="black" points="553.07,-330.19 544.05,-324.64 547.65,-334.61 553.07,-330.19" style=""/>
</g>
<!-- tdgearbox -->
<g id="node23" class="node" pointer-events="visible" data-name="tdgearbox">

<ellipse fill="none" stroke="black" cx="543.68" cy="-162" rx="49.8" ry="18" style=""/>
<text text-anchor="middle" x="543.68" y="-157.8" font-family="Times,serif" font-size="14.00" style="">tdgearbox</text>
</g>
<!-- tdmotor&#45;&gt;tdgearbox -->
<g id="edge22" class="edge" data-name="tdmotor-&gt;tdgearbox">

<path fill="none" stroke="black" d="M531.4,-287.59C533.76,-263.61 538.05,-220.14 540.88,-191.42" style=""/>
<polygon fill="black" stroke="black" points="544.35,-191.91 541.85,-181.62 537.38,-191.23 544.35,-191.91" style=""/>
</g>
<!-- well1 -->
<g id="node24" class="node" pointer-events="visible" data-name="well1">

<ellipse fill="none" stroke="black" cx="775.68" cy="-18" rx="32.48" ry="18" style=""/>
<text text-anchor="middle" x="775.68" y="-13.8" font-family="Times,serif" font-size="14.00" style="">well1</text>
</g>
<!-- tdgearbox&#45;&gt;well1 -->
<g id="edge23" class="edge" data-name="tdgearbox-&gt;well1">

<path fill="none" stroke="black" d="M558.2,-144.61C576.18,-125.12 608.52,-92.65 641.68,-72 671.36,-53.52 708.56,-39.42 736.24,-30.43" style=""/>
<polygon fill="black" stroke="black" points="737.16,-33.81 745.64,-27.46 735.05,-27.13 737.16,-33.81" style=""/>
</g>
<!-- mp_motor7 -->
<g id="node26" class="node" pointer-events="visible" data-name="mp_motor7">

<ellipse fill="none" stroke="black" cx="645.68" cy="-306" rx="56.2" ry="18" style=""/>
<text text-anchor="middle" x="645.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">mp_motor7</text>
</g>
<!-- mp1_inv&#45;&gt;mp_motor7 -->
<g id="edge25" class="edge" data-name="mp1_inv-&gt;mp_motor7">

<path fill="none" stroke="black" d="M672.21,-360.05C668.35,-352.35 663.69,-343.03 659.36,-334.36" style=""/>
<polygon fill="black" stroke="black" points="662.6,-333.01 655,-325.63 656.34,-336.14 662.6,-333.01" style=""/>
</g>
<!-- mp1 -->
<g id="node27" class="node" pointer-events="visible" data-name="mp1">

<ellipse fill="none" stroke="black" cx="678.68" cy="-90" rx="28.41" ry="18" style=""/>
<text text-anchor="middle" x="678.68" y="-85.8" font-family="Times,serif" font-size="14.00" style="">mp1</text>
</g>
<!-- mp_motor7&#45;&gt;mp1 -->
<g id="edge26" class="edge" data-name="mp_motor7-&gt;mp1">

<path fill="none" stroke="black" d="M648.33,-287.85C653.99,-251.14 667.32,-164.66 674.28,-119.53" style=""/>
<polygon fill="black" stroke="black" points="677.73,-120.14 675.79,-109.73 670.81,-119.08 677.73,-120.14" style=""/>
</g>
<!-- mp1&#45;&gt;well1 -->
<g id="edge27" class="edge" data-name="mp1-&gt;well1">

<path fill="none" stroke="black" d="M696.93,-75.83C711.15,-65.57 731.15,-51.14 747.43,-39.38" style=""/>
<polygon fill="black" stroke="black" points="749.23,-42.4 755.29,-33.71 745.14,-36.73 749.23,-42.4" style=""/>
</g>
<!-- mp_motor8 -->
<g id="node29" class="node" pointer-events="visible" data-name="mp_motor8">

<ellipse fill="none" stroke="black" cx="775.68" cy="-306" rx="56.2" ry="18" style=""/>
<text text-anchor="middle" x="775.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">mp_motor8</text>
</g>
<!-- mp2_inv&#45;&gt;mp_motor8 -->
<g id="edge29" class="edge" data-name="mp2_inv-&gt;mp_motor8">

<path fill="none" stroke="black" d="M786.22,-359.7C784.75,-352.32 782.99,-343.52 781.33,-335.25" style=""/>
<polygon fill="black" stroke="black" points="784.79,-334.71 779.4,-325.59 777.93,-336.08 784.79,-334.71" style=""/>
</g>
<!-- mp2 -->
<g id="node30" class="node" pointer-events="visible" data-name="mp2">

<ellipse fill="none" stroke="black" cx="775.68" cy="-90" rx="28.41" ry="18" style=""/>
<text text-anchor="middle" x="775.68" y="-85.8" font-family="Times,serif" font-size="14.00" style="">mp2</text>
</g>
<!-- mp_motor8&#45;&gt;mp2 -->
<g id="edge30" class="edge" data-name="mp_motor8-&gt;mp2">

<path fill="none" stroke="black" d="M775.68,-287.85C775.68,-251.14 775.68,-164.66 775.68,-119.53" style=""/>
<polygon fill="black" stroke="black" points="779.18,-119.75 775.68,-109.75 772.18,-119.75 779.18,-119.75" style=""/>
</g>
<!-- mp2&#45;&gt;well1 -->
<g id="edge31" class="edge" data-name="mp2-&gt;well1">

<path fill="none" stroke="black" d="M775.68,-71.7C775.68,-64.41 775.68,-55.73 775.68,-47.54" style=""/>
<polygon fill="black" stroke="black" points="779.18,-47.62 775.68,-37.62 772.18,-47.62 779.18,-47.62" style=""/>
</g>
<!-- mp_motor9 -->
<g id="node32" class="node" pointer-events="visible" data-name="mp_motor9">

<ellipse fill="none" stroke="black" cx="905.68" cy="-306" rx="56.2" ry="18" style=""/>
<text text-anchor="middle" x="905.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">mp_motor9</text>
</g>
<!-- mp3_inv&#45;&gt;mp_motor9 -->
<g id="edge33" class="edge" data-name="mp3_inv-&gt;mp_motor9">

<path fill="none" stroke="black" d="M904.93,-359.7C905.03,-352.41 905.16,-343.73 905.27,-335.54" style=""/>
<polygon fill="black" stroke="black" points="908.77,-335.67 905.41,-325.62 901.77,-335.57 908.77,-335.67" style=""/>
</g>
<!-- mp3 -->
<g id="node33" class="node" pointer-events="visible" data-name="mp3">

<ellipse fill="none" stroke="black" cx="859.68" cy="-162" rx="28.41" ry="18" style=""/>
<text text-anchor="middle" x="859.68" y="-157.8" font-family="Times,serif" font-size="14.00" style="">mp3</text>
</g>
<!-- mp_motor9&#45;&gt;mp3 -->
<g id="edge34" class="edge" data-name="mp_motor9-&gt;mp3">

<path fill="none" stroke="black" d="M900.13,-287.87C892.32,-263.75 877.99,-219.52 868.65,-190.69" style=""/>
<polygon fill="black" stroke="black" points="871.98,-189.62 865.57,-181.19 865.33,-191.78 871.98,-189.62" style=""/>
</g>
<!-- mp3&#45;&gt;well1 -->
<g id="edge35" class="edge" data-name="mp3-&gt;well1">

<path fill="none" stroke="black" d="M851.61,-144.26C842.69,-126.11 827.6,-96.47 812.68,-72 806.99,-62.67 800.28,-52.77 794.14,-44.09" style=""/>
<polygon fill="black" stroke="black" points="797.1,-42.21 788.43,-36.12 791.41,-46.29 797.1,-42.21" style=""/>
</g>
<!-- dw_motor1 -->
<g id="node35" class="node" pointer-events="visible" data-name="dw_motor1">

<ellipse fill="none" stroke="black" cx="1035.68" cy="-306" rx="55.63" ry="18" style=""/>
<text text-anchor="middle" x="1035.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">dw_motor1</text>
</g>
<!-- invertor1&#45;&gt;dw_motor1 -->
<g id="edge37" class="edge" data-name="invertor1-&gt;dw_motor1">

<path fill="none" stroke="black" d="M1028.15,-359.7C1029.21,-352.32 1030.46,-343.52 1031.65,-335.25" style=""/>
<polygon fill="black" stroke="black" points="1035.07,-336 1033.02,-325.6 1028.14,-335.01 1035.07,-336" style=""/>
</g>
<!-- dw_gearbox1 -->
<g id="node36" class="node" pointer-events="visible" data-name="dw_gearbox1">

<ellipse fill="none" stroke="black" cx="1019.68" cy="-234" rx="63.7" ry="18" style=""/>
<text text-anchor="middle" x="1019.68" y="-229.8" font-family="Times,serif" font-size="14.00" style="">dw_gearbox1</text>
</g>
<!-- dw_motor1&#45;&gt;dw_gearbox1 -->
<g id="edge38" class="edge" data-name="dw_motor1-&gt;dw_gearbox1">

<path fill="none" stroke="black" d="M1031.73,-287.7C1030.04,-280.32 1028.03,-271.52 1026.14,-263.25" style=""/>
<polygon fill="black" stroke="black" points="1029.57,-262.55 1023.93,-253.58 1022.74,-264.11 1029.57,-262.55" style=""/>
</g>
<!-- drum -->
<g id="node37" class="node" pointer-events="visible" data-name="drum">

<ellipse fill="none" stroke="black" cx="1019.68" cy="-162" rx="31.34" ry="18" style=""/>
<text text-anchor="middle" x="1019.68" y="-157.8" font-family="Times,serif" font-size="14.00" style="">drum</text>
</g>
<!-- dw_gearbox1&#45;&gt;drum -->
<g id="edge39" class="edge" data-name="dw_gearbox1-&gt;drum">

<path fill="none" stroke="black" d="M1019.68,-215.7C1019.68,-208.41 1019.68,-199.73 1019.68,-191.54" style=""/>
<polygon fill="black" stroke="black" points="1023.18,-191.62 1019.68,-181.62 1016.18,-191.62 1023.18,-191.62" style=""/>
</g>
<!-- hookblock -->
<g id="node38" class="node" pointer-events="visible" data-name="hookblock">

<ellipse fill="none" stroke="black" cx="970.68" cy="-90" rx="51.61" ry="18" style=""/>
<text text-anchor="middle" x="970.68" y="-85.8" font-family="Times,serif" font-size="14.00" style="">hookblock</text>
</g>
<!-- drum&#45;&gt;hookblock -->
<g id="edge40" class="edge" data-name="drum-&gt;hookblock">

<path fill="none" stroke="black" d="M1008.32,-144.76C1002.52,-136.49 995.34,-126.23 988.81,-116.9" style=""/>
<polygon fill="black" stroke="black" points="991.79,-115.06 983.19,-108.87 986.06,-119.07 991.79,-115.06" style=""/>
</g>
<!-- hookblock&#45;&gt;well1 -->
<g id="edge41" class="edge" data-name="hookblock-&gt;well1">

<path fill="none" stroke="black" d="M935.82,-76.49C901.17,-64.05 848.22,-45.04 812.8,-32.33" style=""/>
<polygon fill="black" stroke="black" points="814.43,-29.19 803.84,-29.11 812.07,-35.78 814.43,-29.19" style=""/>
</g>
<!-- dw_motor2 -->
<g id="node40" class="node" pointer-events="visible" data-name="dw_motor2">

<ellipse fill="none" stroke="black" cx="1164.68" cy="-306" rx="55.63" ry="18" style=""/>
<text text-anchor="middle" x="1164.68" y="-301.8" font-family="Times,serif" font-size="14.00" style="">dw_motor2</text>
</g>
<!-- invertor2&#45;&gt;dw_motor2 -->
<g id="edge43" class="edge" data-name="invertor2-&gt;dw_motor2">

<path fill="none" stroke="black" d="M1157.91,-359.7C1158.85,-352.32 1159.98,-343.52 1161.05,-335.25" style=""/>
<polygon fill="black" stroke="black" points="1164.48,-335.97 1162.29,-325.61 1157.54,-335.08 1164.48,-335.97" style=""/>
</g>
<!-- dw_gearbox2 -->
<g id="node41" class="node" pointer-events="visible" data-name="dw_gearbox2">

<ellipse fill="none" stroke="black" cx="1164.68" cy="-234" rx="63.7" ry="18" style=""/>
<text text-anchor="middle" x="1164.68" y="-229.8" font-family="Times,serif" font-size="14.00" style="">dw_gearbox2</text>
</g>
<!-- dw_motor2&#45;&gt;dw_gearbox2 -->
<g id="edge44" class="edge" data-name="dw_motor2-&gt;dw_gearbox2">

<path fill="none" stroke="black" d="M1164.68,-287.7C1164.68,-280.41 1164.68,-271.73 1164.68,-263.54" style=""/>
<polygon fill="black" stroke="black" points="1168.18,-263.62 1164.68,-253.62 1161.18,-263.62 1168.18,-263.62" style=""/>
</g>
<!-- dw_gearbox2&#45;&gt;drum -->
<g id="edge45" class="edge" data-name="dw_gearbox2-&gt;drum">

<path fill="none" stroke="black" d="M1133.57,-217.98C1109.94,-206.58 1077.49,-190.91 1053.27,-179.21" style=""/>
<polygon fill="black" stroke="black" points="1055,-176.17 1044.48,-174.97 1051.96,-182.47 1055,-176.17" style=""/>
</g>
</g>
</svg>