﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="头文件\Comm">
      <UniqueIdentifier>{bb37d8dd-e3f3-4938-baea-f851033a4c37}</UniqueIdentifier>
    </Filter>
    <Filter Include="源文件\Comm">
      <UniqueIdentifier>{4ae21e07-f5fa-4751-bce5-0083a6f63b89}</UniqueIdentifier>
    </Filter>
    <Filter Include="头文件\Utility">
      <UniqueIdentifier>{2579f0d3-f8a7-4ac8-8417-760861a2349a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="simValue.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="BaseDevice.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PowerSupply.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Motor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Drum.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="GearBox.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pid.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AloLogger.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AloTools.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Hookblock.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Blower.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Pump.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HydralicCylinder.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Invertor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AloValue.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HydralicTong.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Well.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SimulationController.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="EmptyDevice.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PassiveTorqueLoad.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SimpleThreadPool.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Busbar.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Rectifier.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Transformer.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Breaker.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DynamicBrake.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ElectricCylinder.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ThemoModel.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HydralicMotor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ValueMonitor.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ExchangeData.h">
      <Filter>头文件\Comm</Filter>
    </ClInclude>
    <ClInclude Include="VariableHandler.h">
      <Filter>头文件\Utility</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="sim_rig.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="BaseDevice.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PowerSupply.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Motor.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Drum.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="GearBox.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="AloLogger.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Hookblock.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Blower.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Pump.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HydralicCylinder.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Invertor.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HydralicTong.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Well.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SimulationController.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="EmptyDevice.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PassiveTorqueLoad.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Busbar.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Rectifier.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Transformer.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Breaker.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DynamicBrake.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ElectricCylinder.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HydralicMotor.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ExchangeData.cpp">
      <Filter>源文件\Comm</Filter>
    </ClCompile>
  </ItemGroup>
</Project>