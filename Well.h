﻿#pragma once
#include "BaseDevice.h"
namespace sim_rig
{
	//井下，构造：名称，井压系数（0.1~12）0.1:非常松软的泥土，12：坚硬的岩石，一般2.5~3.0之间
	class Well :
		public BaseDevice
	{
	private:
		double every_pipe_weight = 500.0; // 每个钻杆的重量 kg
		double every_pipe_length = 9.5; // 每个钻杆的长度 m
		size_t pipes_num = 0; // 钻杆数量
		double pipes_weight = 0; // 钻杆重量
		double pipes_length = 0; // 钻杆长度
		double well_depth = 0; // 井深
		double bit_pos = 0;//钻头位置
		double bit_speed = 0; // 钻头速度
		double wob = 0;//钻压
		double flowrate = 0.0;
		double flow_gain_loss = 0.0; //泥浆曾孙
	protected:
		double sk = 1.0; // 井压系数,正数，值越大，地层越硬。例如：0.1:非常松软的泥土，10：坚硬的岩石
		double xxk = 1.0; // x^2/(1+x^2)
		double ek = 1.0; //e^x/(1+e^x)
		double xk = 1.0; //x/(1+x)
	public:
		// grd_ratio:井压系数,正数，值越大，地层越硬。例如：0.1:非常松软的泥土，12：坚硬的岩石，一般2.5~3.0之间
		Well(const std::string& name, double grd_ratio)
			:BaseDevice(name), sk(grd_ratio)
		{
			sk = grd_ratio;
			sk = std::clamp(sk, 0.1, 12.0);
			xk = sk / (1.0 + sk);
			xxk = xk * xk / (1.0 + xk * xk);
			ek = exp(xk) / (1.0 + exp(xk));

			description = "井下设备";
			// 注册变量
			register_value("ref_pipes_count", "参考钻杆数量", pipes_num);
			register_value("act_pipes_weight", "实际钻杆重量 kg", pipes_weight);
			register_value("act_pipes_length", "实际钻杆长度 m", pipes_length);
			register_value("ref_well_depth", "参考井深 m", well_depth);
			register_value("ref_bit_pos", "参考钻头位置 m", bit_pos);
			register_value("act_bit_speed", "实际钻头速度 rpm", bit_speed);
			register_value("act_WOB", "实际钻压 kg", wob);
			register_value("act_flowrate", "实际排量 L/min", flowrate);
			register_value("act_flow_gain_loss", "实际泥浆溢流状况 m3", flow_gain_loss);
			register_value("ref_every_pipe_weight", "参考每根钻杆重量 kg", every_pipe_weight);
			register_value("ref_every_pipe_length", "参考每根钻杆长度 m", every_pipe_length);
			register_value("ref_indentaion_hardness", "参考史氏硬度（压入硬度）（0.1~1:泥土，1~12：岩石）", sk);
		}

		//设置井下泥浆溢流状况
		void set_flow_gain_loss(double gain_loss) { flow_gain_loss = gain_loss; }

		//增加钻杆
		void add_pipe(int count = 1) { pipes_num += count; }
		//减少钻杆
		void sub_pipe(int count = 1) { pipes_num -= count; pipes_num = std::max<size_t>(pipes_num, 0); }
		//设置钻杆数量
		void set_pipe_count(int count) { pipes_num = count; }
		//获取井深
		double get_well_depth()const { return well_depth; }
		//获取钻头位置
		double get_bit_pos()const { return bit_pos; }
		//获取钻头速度
		double get_bit_speed()const { return bit_speed; }
		//获取钻压 kg
		double get_wob()const { return wob; }
		//获取钻杆重量kg
		double get_pipes_weight()const { return pipes_weight; }
		//获取钻杆长度 m
		double get_pipes_length()const { return pipes_length; }
		//获取钻杆数量
		size_t get_pipes_num()const { return pipes_num; }
		//设置​史氏硬度（压入硬度）（0.1~1:泥土，1~12：岩石）
		void set_indentaion_hardness(double hd) { sk = hd; }
		//获取排量
		double get_flowrate()const { return flowrate; }
		//设置井深
		void set_well_depth(double depth) { well_depth = depth; }
		//设置钻头位置
		void set_bit_pos(double pos) { bit_pos = pos; }
	protected:
		// 判断是否允许子级连接
		virtual bool can_child_connect(std::shared_ptr<BaseDevice> child) override;
		// 1.计算本级需求
		virtual void compute_this_demand(double delta_time) override;
		// 2.计算子级需求
		virtual void compute_children_demand(double delta_time) override;
		// 3.计算本级供应，传递给子级。
		virtual void compute_this_supply(double delta_time) override;
		// 4.计算父级供应，传递给子级
		virtual void compute_parents_supply(double delta_time) override;
		// 5.处理本级反馈
		virtual void compute_this_feedback(double delta_time) override;
		// 6.计算子级反馈
		virtual void compute_children_feedback(double delta_time) override;
		// 7.慢循环中，数据监控
		virtual void monitor_values(double delta_time) override;
	};
}

