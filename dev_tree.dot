digraph DeviceTree {
    "root";
    "root" -> "power";
    "power";
    "power" -> "breaker1";
    "breaker1";
    "breaker1" -> "bus600vbar";
    "bus600vbar";
    "bus600vbar" -> "transformer1";
    "transformer1";
    "transformer1" -> "breaker2";
    "breaker2";
    "breaker2" -> "bus400vbar";
    "bus400vbar";
    "bus400vbar" -> "motor20";
    "motor20";
    "bus400vbar" -> "motor6";
    "motor6";
    "motor6" -> "blower2";
    "blower2";
    "bus400vbar" -> "mt100";
    "mt100";
    "bus400vbar" -> "mt101";
    "mt101";
    "bus400vbar" -> "mt102";
    "mt102";
    "bus400vbar" -> "mt103";
    "mt103";
    "bus400vbar" -> "mt104";
    "mt104";
    "bus400vbar" -> "mt105";
    "mt105";
    "bus400vbar" -> "brake_pump_motor";
    "brake_pump_motor";
    "brake_pump_motor" -> "brake_pump";
    "brake_pump";
    "brake_pump" -> "nc_cylinder1";
    "nc_cylinder1";
    "nc_cylinder1" -> "btong2";
    "btong2";
    "btong2" -> "drum";
    "drum";
    "drum" -> "hookblock";
    "hookblock";
    "hookblock" -> "well1";
    "well1";
    "brake_pump" -> "no_cylinder2";
    "no_cylinder2";
    "no_cylinder2" -> "btong1";
    "btong1";
    "btong1" -> "drum";
    "bus400vbar" -> "motor3";
    "motor3";
    "bus400vbar" -> "motor4";
    "motor4";
    "motor4" -> "blower1";
    "blower1";
    "bus400vbar" -> "motor5";
    "motor5";
    "motor5" -> "pump1";
    "pump1";
    "pump1" -> "hdy_motor1";
    "hdy_motor1";
    "pump1" -> "cylinder1";
    "cylinder1";
    "pump1" -> "cylinder2";
    "cylinder2";
    "bus600vbar" -> "breaker3";
    "breaker3";
    "breaker3" -> "rectifier1";
    "rectifier1";
    "rectifier1" -> "busdcbar1";
    "busdcbar1";
    "busdcbar1" -> "dynamic_brake1";
    "dynamic_brake1";
    "busdcbar1" -> "tdinv";
    "tdinv";
    "tdinv" -> "tdmotor";
    "tdmotor";
    "tdmotor" -> "tdgearbox";
    "tdgearbox";
    "tdgearbox" -> "well1";
    "busdcbar1" -> "invertor1";
    "invertor1";
    "invertor1" -> "dw_motor1";
    "dw_motor1";
    "dw_motor1" -> "dw_gearbox1";
    "dw_gearbox1";
    "dw_gearbox1" -> "drum";
    "busdcbar1" -> "invertor2";
    "invertor2";
    "invertor2" -> "dw_motor2";
    "dw_motor2";
    "dw_motor2" -> "dw_gearbox2";
    "dw_gearbox2";
    "dw_gearbox2" -> "drum";
    "bus600vbar" -> "breaker5";
    "breaker5";
    "breaker5" -> "rectifier2";
    "rectifier2";
    "rectifier2" -> "busdcbar2";
    "busdcbar2";
    "busdcbar2" -> "dynamic_brake2";
    "dynamic_brake2";
    "busdcbar2" -> "mp1_inv";
    "mp1_inv";
    "mp1_inv" -> "mp_motor7";
    "mp_motor7";
    "mp_motor7" -> "mp1";
    "mp1";
    "mp1" -> "well1";
    "busdcbar2" -> "mp2_inv";
    "mp2_inv";
    "mp2_inv" -> "mp_motor8";
    "mp_motor8";
    "mp_motor8" -> "mp2";
    "mp2";
    "mp2" -> "well1";
    "busdcbar2" -> "mp3_inv";
    "mp3_inv";
    "mp3_inv" -> "mp_motor9";
    "mp_motor9";
    "mp_motor9" -> "mp3";
    "mp3";
    "mp3" -> "well1";
}
