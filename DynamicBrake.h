﻿#pragma once
#include "BaseDevice.h"
namespace sim_rig
{
	//能耗制动，构造：名称，电阻值、上限电压、下限电压
	class DynamicBrake :
		public BaseDevice
	{
	private:
		double upper_volt = 970.0; //上限电压
		double lower_volt = 1010.0; //下限电压
		double brake_power = 1000.0; //制动功率
		double R = 2.3; //电阻
		bool is_braking = false; //是否正在制动
	private:

		// 物理参数 - 针对大功率制动电阻进行调整
		const double mass = 150.0;         // kg, 电阻质量
		const double specificHeat = 500.0; // J/(kg·K), 假设为钢材的比热容
		const double surfaceArea = 8.0;    // m², 增大散热片的等效散热面积

		// 冷却系数 - 加强强制风冷效果
		const double naturalCoolingCoeff = 25.0;  // W/(m²·K), 自然对流换热系数
		const double forcedCoolingCoeff = 1000.0; // W/(m²·K), 显著提高强制风冷系数

		// 状态变量
		double currentTemp = 0;       // 当前温度(°C)
		const double ambientTemp = 25.0; // 环境温度(°C)
		bool isConnected;         // 是否接入母线 (is_on)
		bool isForcedCooling = true;     // 强制冷却状态

		// 电气参数
		double voltage = 0;    // 电压(V)
		double power = 0;      // 实际工作功率(kW), P = V^2 / R
		double current = 0;

	protected:
		void calctemperature(double delta_time);
	public:
		DynamicBrake(const std::string& name, double resistance, double uppervolt, double lowervolt)
			: BaseDevice(name), upper_volt(uppervolt), lower_volt(lowervolt), R(resistance)
		{
			isConnected = false;
			description = "能耗制动";
			// 注册变量
			register_value("act_voltage", "电压(V)", voltage);
			register_value("act_power", "功率(kW)", power);
			register_value("act_current", "电流(A)", current);
			register_value("act_temperature", "制动电阻温度 c", currentTemp);

			add_monitor<double>("temperature", AloVM::Rule<double>{
				.delay = 5.0,
					.level = AloVM::Level::WARNING,
					.description = "制动电阻温度超过报警！",
					.cond = [](auto v)
					{
						return v > 150.0;
					},
					.callback = [&](auto t, auto info)
					{
						trigger_fault("制动电阻温度超过报警! 温度=" + std::to_string(t), false);
					}
			});

			add_monitor<double>("temperature", AloVM::Rule<double>{
				.delay = 5.0,
					.level = AloVM::Level::FAULT,
					.description = "制动电阻温度过高！",
					.cond = [](auto v)
					{
						return v > 180.0;
					},
					.callback = [&](auto t, auto info)
					{
						trigger_fault("制动电阻温度高温故障! 温度=" + std::to_string(t), false);
					}
			});
		}
		double get_volt() const { return voltage; }
		double get_power() const { return power; }
		double get_current() const { return current; }

	protected:
		// 叶节点，不能有任何子节点
		virtual bool can_child_connect(std::shared_ptr<BaseDevice> child) override { return false; }
		virtual bool can_connect_to_parent(std::shared_ptr<BaseDevice> parent) override
		{
			// 只能连接一个父级
			return  parents.size() < 1;
		}
	protected:

		// 1.计算本级需求
		virtual void compute_this_demand(double delta_time) override;
		// 2.计算子级需求
		virtual void compute_children_demand(double delta_time) override;
		// 3.计算本级供应，传递给子级。
		virtual void compute_this_supply(double delta_time) override;
		// 4.计算父级供应，传递给子级
		virtual void compute_parents_supply(double delta_time) override;
		// 5.处理本级反馈
		virtual void compute_this_feedback(double delta_time) override;
		// 6.计算子级反馈
		virtual void compute_children_feedback(double delta_time) override;
		// 7.慢循环中，数据监控
		virtual void monitor_values(double delta_time) override;
	};

}

