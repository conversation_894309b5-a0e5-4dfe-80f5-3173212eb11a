#include <vector>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <future>
#include <atomic>
#include <iostream>

class SimpleThreadPool
{
public:
	explicit SimpleThreadPool(size_t thread_count) : stop_flag(false)
	{
		for (size_t i = 0; i < thread_count; ++i)
		{
			workers.emplace_back([this]
				{
					while (true) {
						std::function<void()> task;
						{
							std::unique_lock<std::mutex> lock(queue_mutex);
							cond_var.wait(lock, [this] { return stop_flag || !tasks.empty(); });
							if (stop_flag && tasks.empty()) return;
							task = std::move(tasks.front());
							tasks.pop();
						}
						task();
					} });
		}
	}

	template <class F, class... Args>
	auto submit(F&& f, Args &&...args)
		-> std::future<typename std::invoke_result<F, Args...>::type>
	{
		using return_type = typename std::invoke_result<F, Args...>::type;
		auto task = std::make_shared<std::packaged_task<return_type()>>(
			std::bind(std::forward<F>(f), std::forward<Args>(args)...));
		std::future<return_type> res = task->get_future();
		{
			std::unique_lock<std::mutex> lock(queue_mutex);
			tasks.emplace([task]()
				{ (*task)(); });
		}
		cond_var.notify_one();
		return res;
	}

	~SimpleThreadPool()
	{
		{
			std::unique_lock<std::mutex> lock(queue_mutex);
			stop_flag = true;
		}
		cond_var.notify_all();
		for (auto& worker : workers)
		{
			if (worker.joinable())
				worker.join();
		}
	}

private:
	std::vector<std::thread> workers;
	std::queue<std::function<void()>> tasks;
	std::mutex queue_mutex;
	std::condition_variable cond_var;
	std::atomic<bool> stop_flag;
};