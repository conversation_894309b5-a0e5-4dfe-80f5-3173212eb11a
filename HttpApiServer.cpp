#include "HttpApiServer.h"
#include "AloLogger.h"
#include <iostream>
#include <sstream>

namespace sim_rig
{
    HttpApiServer::HttpApiServer(std::shared_ptr<SimulationController> sim_controller, int port)
        : sim_controller_(sim_controller), port_(port)
    {
        server_ = std::make_unique<httplib::Server>();
        setup_routes();
        enable_cors();
    }

    HttpApiServer::~HttpApiServer()
    {
        stop();
    }

    bool HttpApiServer::start()
    {
        if (is_running_.load())
        {
            return false; // 已经在运行
        }

        server_thread_ = std::thread([this]()
                                     {
            is_running_.store(true);
            Logger::getInstance().info("HTTP API服务器启动在端口: " + std::to_string(port_));
            
            if (!server_->listen("0.0.0.0", port_)) {
                Logger::getInstance().error("HTTP API服务器启动失败");
                is_running_.store(false);
            } });

        // 等待一小段时间确保服务器启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        return is_running_.load();
    }

    void HttpApiServer::stop()
    {
        if (!is_running_.load())
        {
            return; // 已经停止
        }

        is_running_.store(false);
        if (server_)
        {
            server_->stop();
        }

        if (server_thread_.joinable())
        {
            server_thread_.join();
        }

        Logger::getInstance().info("HTTP API服务器已停止");
    }

    void HttpApiServer::enable_cors()
    {
        server_->set_pre_routing_handler([](const httplib::Request &req, httplib::Response &res)
                                         {
            res.set_header("Access-Control-Allow-Origin", "*");
            res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
            return httplib::Server::HandlerResponse::Unhandled; });

        // 处理OPTIONS预检请求
        server_->Options(".*", [](const httplib::Request &, httplib::Response &res)
                         {
            res.status = 200;
            return; });
    }

    nlohmann::json HttpApiServer::create_success_response(const nlohmann::json &data)
    {
        nlohmann::json response;
        response["success"] = true;
        response["data"] = data;
        response["timestamp"] = std::time(nullptr);
        return response;
    }

    nlohmann::json HttpApiServer::create_error_response(const std::string &message, int code)
    {
        nlohmann::json response;
        response["success"] = false;
        response["error"] = {
            {"code", code},
            {"message", message}};
        response["timestamp"] = std::time(nullptr);
        return response;
    }

    std::shared_ptr<BaseDevice> HttpApiServer::get_device_by_name(const std::string &name)
    {
        if (!sim_controller_)
        {
            return nullptr;
        }
        return sim_controller_->get_device(name);
    }

    nlohmann::json HttpApiServer::device_to_json(std::shared_ptr<BaseDevice> device)
    {
        if (!device)
        {
            return nlohmann::json::object();
        }

        nlohmann::json dev_json;
        dev_json["name"] = device->get_name();
        dev_json["description"] = device->get_description();
        dev_json["status"] = device->get_status_str();
        dev_json["is_working"] = device->is_working();
        dev_json["is_fault"] = device->is_fault();
        dev_json["node_online"] = device->node_is_online();

        // 获取设备变量
        auto var_names = device->get_value_labels();
        nlohmann::json variables = nlohmann::json::object();

        for (const auto &var_name : var_names)
        {
            try
            {
                variables[var_name] = variable_to_json(device, var_name);
            }
            catch (const std::exception &e)
            {
                Logger::getInstance().error("获取变量失败: " + var_name + ", 错误: " + e.what());
            }
        }

        dev_json["variables"] = variables;

        // 获取子设备和父设备信息
        nlohmann::json children = nlohmann::json::array();
        device->for_each_child([&children](std::shared_ptr<BaseDevice> child)
                               { children.push_back(child->get_name()); });
        dev_json["children"] = children;

        nlohmann::json parents = nlohmann::json::array();
        device->for_each_parent([&parents](std::shared_ptr<BaseDevice> parent)
                                { parents.push_back(parent->get_name()); });
        dev_json["parents"] = parents;

        return dev_json;
    }

    nlohmann::json HttpApiServer::variable_to_json(std::shared_ptr<BaseDevice> device, const std::string &var_name)
    {
        if (!device)
        {
            return nlohmann::json::object();
        }

        nlohmann::json var_json;
        var_json["name"] = var_name;
        var_json["description"] = device->get_value_description(var_name);

        // 尝试获取不同类型的值
        try
        {
            // 先尝试double类型
            auto double_val = device->get_value<double>(var_name);
            var_json["value"] = double_val;
            var_json["type"] = "double";
        }
        catch (...)
        {
            try
            {
                // 尝试bool类型
                auto bool_val = device->get_value<bool>(var_name);
                var_json["value"] = bool_val;
                var_json["type"] = "bool";
            }
            catch (...)
            {
                try
                {
                    // 尝试int类型
                    auto int_val = device->get_value<int>(var_name);
                    var_json["value"] = int_val;
                    var_json["type"] = "int";
                }
                catch (...)
                {
                    try
                    {
                        // 尝试string类型
                        auto str_val = device->get_value<std::string>(var_name);
                        var_json["value"] = str_val;
                        var_json["type"] = "string";
                    }
                    catch (...)
                    {
                        var_json["value"] = nullptr;
                        var_json["type"] = "unknown";
                        var_json["error"] = "无法获取变量值";
                    }
                }
            }
        }

        return var_json;
    }

    void HttpApiServer::setup_routes()
    {
        // 设备管理接口
        server_->Get("/api/devices", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_devices(req, res); });

        server_->Get(R"(/api/devices/([^/]+))", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_device(req, res); });

        server_->Get(R"(/api/devices/([^/]+)/variables)", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_device_variables(req, res); });

        server_->Get(R"(/api/devices/([^/]+)/tree)", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_device_tree(req, res); });

        // 变量读写接口
        server_->Get(R"(/api/devices/([^/]+)/variables/([^/]+))", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_variable(req, res); });

        server_->Post(R"(/api/devices/([^/]+)/variables/([^/]+))", [this](const httplib::Request &req, httplib::Response &res)
                      { handle_set_variable(req, res); });

        server_->Post(R"(/api/devices/([^/]+)/variables)", [this](const httplib::Request &req, httplib::Response &res)
                      { handle_set_variables_batch(req, res); });

        // 设备控制接口
        server_->Post(R"(/api/devices/([^/]+)/actions/([^/]+))", [this](const httplib::Request &req, httplib::Response &res)
                      { handle_device_action(req, res); });

        // 仿真控制接口
        server_->Get("/api/simulation/status", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_simulation_status(req, res); });

        server_->Post(R"(/api/simulation/([^/]+))", [this](const httplib::Request &req, httplib::Response &res)
                      { handle_simulation_control(req, res); });

        server_->Get("/api/simulation/stats", [this](const httplib::Request &req, httplib::Response &res)
                     { handle_get_simulation_stats(req, res); });

        // 根路径返回API文档
        server_->Get("/", [this](const httplib::Request &req, httplib::Response &res)
                     { res.set_content(R"(
<!DOCTYPE html>
<html>
<head>
    <title>设备仿真系统 HTTP API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1, h2 { color: #333; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <h1>设备仿真系统 HTTP API</h1>
    <h2>设备管理接口</h2>
    <div class="endpoint"><span class="method">GET</span> /api/devices - 获取所有设备列表</div>
    <div class="endpoint"><span class="method">GET</span> /api/devices/{name} - 获取指定设备信息</div>
    <div class="endpoint"><span class="method">GET</span> /api/devices/{name}/variables - 获取设备变量列表</div>
    <div class="endpoint"><span class="method">GET</span> /api/devices/{name}/tree - 获取设备树结构</div>

    <h2>变量读写接口</h2>
    <div class="endpoint"><span class="method">GET</span> /api/devices/{name}/variables/{var_name} - 读取设备变量值</div>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/variables/{var_name} - 设置设备变量值</div>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/variables - 批量设置多个变量</div>

    <h2>设备控制接口</h2>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/actions/start - 启动设备</div>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/actions/stop - 停止设备</div>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/actions/reset - 重置设备</div>
    <div class="endpoint"><span class="method">POST</span> /api/devices/{name}/actions/switch - 设备开关控制</div>

    <h2>仿真控制接口</h2>
    <div class="endpoint"><span class="method">GET</span> /api/simulation/status - 获取仿真状态</div>
    <div class="endpoint"><span class="method">POST</span> /api/simulation/start - 启动仿真</div>
    <div class="endpoint"><span class="method">POST</span> /api/simulation/stop - 停止仿真</div>
    <div class="endpoint"><span class="method">GET</span> /api/simulation/stats - 获取性能统计</div>
</body>
</html>
            )",
                                       "text/html"); });
    }

    // 包含处理函数的实现
    void HttpApiServer::handle_get_devices(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            if (!sim_controller_)
            {
                auto error_response = create_error_response("仿真控制器未初始化", 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
                return;
            }

            auto device_names = sim_controller_->get_all_device_names();
            nlohmann::json devices_array = nlohmann::json::array();

            for (const auto &name : device_names)
            {
                auto device = sim_controller_->get_device(name);
                if (device)
                {
                    nlohmann::json device_info;
                    device_info["name"] = device->get_name();
                    device_info["description"] = device->get_description();
                    device_info["status"] = device->get_status_str();
                    device_info["is_working"] = device->is_working();
                    device_info["node_online"] = device->node_is_online();
                    devices_array.push_back(device_info);
                }
            }

            auto response = create_success_response(devices_array);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取设备列表失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_get_device(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            auto device = get_device_by_name(device_name);

            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            auto device_json = device_to_json(device);
            auto response = create_success_response(device_json);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取设备信息失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_get_device_variables(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            auto device = get_device_by_name(device_name);

            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            auto var_names = device->get_value_labels();
            nlohmann::json variables = nlohmann::json::object();

            for (const auto &var_name : var_names)
            {
                try
                {
                    variables[var_name] = variable_to_json(device, var_name);
                }
                catch (const std::exception &e)
                {
                    Logger::getInstance().error("获取变量失败: " + var_name + ", 错误: " + e.what());
                }
            }

            auto response = create_success_response(variables);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取设备变量失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_get_device_tree(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            auto device = get_device_by_name(device_name);

            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            std::string tree_str = device->get_tree();
            nlohmann::json tree_data;
            tree_data["device_name"] = device_name;
            tree_data["tree_structure"] = tree_str;

            auto response = create_success_response(tree_data);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取设备树失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    // 变量读写接口实现
    void HttpApiServer::handle_get_variable(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            std::string var_name = req.matches[2];

            auto device = get_device_by_name(device_name);
            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            auto var_json = variable_to_json(device, var_name);
            if (var_json.contains("error"))
            {
                auto error_response = create_error_response("变量不存在或无法访问: " + var_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            auto response = create_success_response(var_json);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取变量值失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_set_variable(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            std::string var_name = req.matches[2];

            auto device = get_device_by_name(device_name);
            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            // 解析JSON请求体
            nlohmann::json request_json;
            try
            {
                request_json = nlohmann::json::parse(req.body);
            }
            catch (const std::exception &e)
            {
                auto error_response = create_error_response("无效的JSON格式: " + std::string(e.what()), 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            if (!request_json.contains("value"))
            {
                auto error_response = create_error_response("请求体必须包含'value'字段", 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            bool success = set_device_variable_from_json(device, var_name, request_json["value"]);
            if (!success)
            {
                auto error_response = create_error_response("设置变量值失败: " + var_name, 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            // 返回设置后的变量值
            auto var_json = variable_to_json(device, var_name);
            auto response = create_success_response(var_json);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("设置变量值失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_set_variables_batch(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];

            auto device = get_device_by_name(device_name);
            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            // 解析JSON请求体
            nlohmann::json request_json;
            try
            {
                request_json = nlohmann::json::parse(req.body);
            }
            catch (const std::exception &e)
            {
                auto error_response = create_error_response("无效的JSON格式: " + std::string(e.what()), 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            if (!request_json.contains("variables") || !request_json["variables"].is_object())
            {
                auto error_response = create_error_response("请求体必须包含'variables'对象", 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            nlohmann::json results = nlohmann::json::object();
            nlohmann::json errors = nlohmann::json::object();

            for (auto &[var_name, value] : request_json["variables"].items())
            {
                bool success = set_device_variable_from_json(device, var_name, value);
                if (success)
                {
                    results[var_name] = variable_to_json(device, var_name);
                }
                else
                {
                    errors[var_name] = "设置失败";
                }
            }

            nlohmann::json response_data;
            response_data["success_count"] = results.size();
            response_data["error_count"] = errors.size();
            response_data["results"] = results;
            if (!errors.empty())
            {
                response_data["errors"] = errors;
            }

            auto response = create_success_response(response_data);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("批量设置变量失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    // 设备控制接口实现
    void HttpApiServer::handle_device_action(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string device_name = req.matches[1];
            std::string action = req.matches[2];

            auto device = get_device_by_name(device_name);
            if (!device)
            {
                auto error_response = create_error_response("设备不存在: " + device_name, 404);
                res.set_content(error_response.dump(), "application/json");
                res.status = 404;
                return;
            }

            nlohmann::json result;
            bool success = false;

            if (action == "start")
            {
                device->set_status(DeviceStatus::RUNNING);
                result["action"] = "start";
                result["status"] = device->get_status_str();
                success = true;
            }
            else if (action == "stop")
            {
                device->set_status(DeviceStatus::STOP);
                result["action"] = "stop";
                result["status"] = device->get_status_str();
                success = true;
            }
            else if (action == "reset")
            {
                device->device_reset();
                result["action"] = "reset";
                result["status"] = device->get_status_str();
                success = true;
            }
            else if (action == "switch")
            {
                // 解析请求体获取开关状态
                nlohmann::json request_json;
                bool switch_on = true; // 默认开启

                if (!req.body.empty())
                {
                    try
                    {
                        request_json = nlohmann::json::parse(req.body);
                        if (request_json.contains("on"))
                        {
                            switch_on = request_json["on"].get<bool>();
                        }
                    }
                    catch (const std::exception &e)
                    {
                        auto error_response = create_error_response("无效的JSON格式: " + std::string(e.what()), 400);
                        res.set_content(error_response.dump(), "application/json");
                        res.status = 400;
                        return;
                    }
                }

                device->device_switch(switch_on);
                result["action"] = "switch";
                result["switch_on"] = switch_on;
                result["status"] = device->get_status_str();
                success = true;
            }
            else
            {
                auto error_response = create_error_response("不支持的操作: " + action, 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            if (success)
            {
                result["device_name"] = device_name;
                auto response = create_success_response(result);
                res.set_content(response.dump(), "application/json");
                res.status = 200;
            }
            else
            {
                auto error_response = create_error_response("执行操作失败: " + action, 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
            }
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("设备操作失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    // 仿真控制接口实现
    void HttpApiServer::handle_get_simulation_status(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            if (!sim_controller_)
            {
                auto error_response = create_error_response("仿真控制器未初始化", 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
                return;
            }

            nlohmann::json status_data;
            auto state = sim_controller_->get_state();

            switch (state)
            {
            case SimulationState::STOPPED:
                status_data["state"] = "STOPPED";
                break;
            case SimulationState::RUNNING:
                status_data["state"] = "RUNNING";
                break;
            case SimulationState::PAUSED:
                status_data["state"] = "PAUSED";
                break;
            default:
                status_data["state"] = "UNKNOWN";
                break;
            }

            status_data["is_running"] = sim_controller_->is_running();
            status_data["is_stopped"] = sim_controller_->is_stopped();
            status_data["device_count"] = sim_controller_->get_device_count();
            status_data["target_fps"] = sim_controller_->get_target_fps();
            status_data["delta_time_limit"] = sim_controller_->get_delta_time_limit();

            auto response = create_success_response(status_data);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取仿真状态失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_simulation_control(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string action = req.matches[1];

            if (!sim_controller_)
            {
                auto error_response = create_error_response("仿真控制器未初始化", 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
                return;
            }

            nlohmann::json result;
            bool success = false;

            if (action == "start")
            {
                // 解析请求体获取多线程选项
                bool multi_thread = false;
                if (!req.body.empty())
                {
                    try
                    {
                        auto request_json = nlohmann::json::parse(req.body);
                        if (request_json.contains("multi_thread"))
                        {
                            multi_thread = request_json["multi_thread"].get<bool>();
                        }
                    }
                    catch (const std::exception &e)
                    {
                        // 忽略JSON解析错误，使用默认值
                    }
                }

                success = sim_controller_->start_simulation(multi_thread);
                result["action"] = "start";
                result["multi_thread"] = multi_thread;
            }
            else if (action == "stop")
            {
                success = sim_controller_->stop_simulation();
                result["action"] = "stop";
            }
            else if (action == "pause")
            {
                success = sim_controller_->pause_simulation();
                result["action"] = "pause";
            }
            else if (action == "resume")
            {
                success = sim_controller_->resume_simulation();
                result["action"] = "resume";
            }
            else
            {
                auto error_response = create_error_response("不支持的仿真操作: " + action, 400);
                res.set_content(error_response.dump(), "application/json");
                res.status = 400;
                return;
            }

            if (success)
            {
                auto state = sim_controller_->get_state();
                switch (state)
                {
                case SimulationState::STOPPED:
                    result["state"] = "STOPPED";
                    break;
                case SimulationState::RUNNING:
                    result["state"] = "RUNNING";
                    break;
                case SimulationState::PAUSED:
                    result["state"] = "PAUSED";
                    break;
                default:
                    result["state"] = "UNKNOWN";
                    break;
                }

                auto response = create_success_response(result);
                res.set_content(response.dump(), "application/json");
                res.status = 200;
            }
            else
            {
                auto error_response = create_error_response("执行仿真操作失败: " + action, 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
            }
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("仿真控制失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    void HttpApiServer::handle_get_simulation_stats(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            if (!sim_controller_)
            {
                auto error_response = create_error_response("仿真控制器未初始化", 500);
                res.set_content(error_response.dump(), "application/json");
                res.status = 500;
                return;
            }

            auto stats = sim_controller_->get_performance_stats();

            nlohmann::json stats_data;
            stats_data["avg_frame_time"] = stats.avg_frame_time;
            stats_data["min_frame_time"] = stats.min_frame_time;
            stats_data["max_frame_time"] = stats.max_frame_time;
            stats_data["fps"] = stats.fps;
            stats_data["total_frames"] = stats.total_frames;

            // 添加额外的统计信息
            stats_data["avg_frame_time_ms"] = stats.avg_frame_time * 1000.0;
            stats_data["min_frame_time_ms"] = stats.min_frame_time * 1000.0;
            stats_data["max_frame_time_ms"] = stats.max_frame_time * 1000.0;

            auto response = create_success_response(stats_data);
            res.set_content(response.dump(), "application/json");
            res.status = 200;
        }
        catch (const std::exception &e)
        {
            auto error_response = create_error_response("获取性能统计失败: " + std::string(e.what()), 500);
            res.set_content(error_response.dump(), "application/json");
            res.status = 500;
        }
    }

    // 辅助函数实现
    bool HttpApiServer::set_device_variable_from_json(std::shared_ptr<BaseDevice> device,
                                                      const std::string &var_name,
                                                      const nlohmann::json &value)
    {
        if (!device)
        {
            return false;
        }

        try
        {
            // 根据JSON值的类型尝试设置变量
            if (value.is_boolean())
            {
                return device->set_value<bool>(var_name, value.get<bool>());
            }
            else if (value.is_number_integer())
            {
                // 尝试int类型
                if (device->set_value<int>(var_name, value.get<int>()))
                {
                    return true;
                }
                // 如果int失败，尝试double
                return device->set_value<double>(var_name, value.get<double>());
            }
            else if (value.is_number_float())
            {
                return device->set_value<double>(var_name, value.get<double>());
            }
            else if (value.is_string())
            {
                return device->set_value<std::string>(var_name, value.get<std::string>());
            }
            else
            {
                return false; // 不支持的类型
            }
        }
        catch (const std::exception &e)
        {
            Logger::getInstance().error("设置变量失败: " + var_name + ", 错误: " + e.what());
            return false;
        }
    }

    nlohmann::json HttpApiServer::get_device_variable_as_json(std::shared_ptr<BaseDevice> device,
                                                              const std::string &var_name)
    {
        return variable_to_json(device, var_name);
    }
}
