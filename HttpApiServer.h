#pragma once

#include <memory>
#include <string>
#include <thread>
#include <atomic>
#include <functional>
#include "httplib.h"
#include "json.hpp"
#include "SimulationController.h"

namespace sim_rig
{
    /**
     * @brief HTTP API服务器类
     * 
     * 提供RESTful API接口来访问和控制设备仿真系统
     * 支持设备信息查询、变量读写、设备控制等功能
     */
    class HttpApiServer
    {
    private:
        std::unique_ptr<httplib::Server> server_;
        std::thread server_thread_;
        std::atomic<bool> is_running_{false};
        int port_;
        std::shared_ptr<SimulationController> sim_controller_;
        
        // JSON响应辅助函数
        nlohmann::json create_success_response(const nlohmann::json& data = nlohmann::json::object());
        nlohmann::json create_error_response(const std::string& message, int code = 400);
        
        // 设备信息转换函数
        nlohmann::json device_to_json(std::shared_ptr<BaseDevice> device);
        nlohmann::json variable_to_json(std::shared_ptr<BaseDevice> device, const std::string& var_name);
        
        // API路由处理函数
        void setup_routes();
        
        // 设备管理接口
        void handle_get_devices(const httplib::Request& req, httplib::Response& res);
        void handle_get_device(const httplib::Request& req, httplib::Response& res);
        void handle_get_device_variables(const httplib::Request& req, httplib::Response& res);
        void handle_get_device_tree(const httplib::Request& req, httplib::Response& res);
        
        // 变量读写接口
        void handle_get_variable(const httplib::Request& req, httplib::Response& res);
        void handle_set_variable(const httplib::Request& req, httplib::Response& res);
        void handle_set_variables_batch(const httplib::Request& req, httplib::Response& res);
        
        // 设备控制接口
        void handle_device_action(const httplib::Request& req, httplib::Response& res);
        
        // 仿真控制接口
        void handle_get_simulation_status(const httplib::Request& req, httplib::Response& res);
        void handle_simulation_control(const httplib::Request& req, httplib::Response& res);
        void handle_get_simulation_stats(const httplib::Request& req, httplib::Response& res);
        
        // 辅助函数
        std::shared_ptr<BaseDevice> get_device_by_name(const std::string& name);
        bool set_device_variable_from_json(std::shared_ptr<BaseDevice> device, 
                                          const std::string& var_name, 
                                          const nlohmann::json& value);
        nlohmann::json get_device_variable_as_json(std::shared_ptr<BaseDevice> device, 
                                                  const std::string& var_name);
        
    public:
        /**
         * @brief 构造函数
         * @param sim_controller 仿真控制器指针
         * @param port HTTP服务器端口，默认8080
         */
        HttpApiServer(std::shared_ptr<SimulationController> sim_controller, int port = 8080);
        
        /**
         * @brief 析构函数
         */
        ~HttpApiServer();
        
        /**
         * @brief 启动HTTP服务器
         * @return true 启动成功，false 启动失败
         */
        bool start();
        
        /**
         * @brief 停止HTTP服务器
         */
        void stop();
        
        /**
         * @brief 检查服务器是否正在运行
         * @return true 正在运行，false 已停止
         */
        bool is_running() const { return is_running_.load(); }
        
        /**
         * @brief 获取服务器端口
         * @return 端口号
         */
        int get_port() const { return port_; }
        
        /**
         * @brief 设置CORS头部（允许跨域访问）
         */
        void enable_cors();
    };
}
