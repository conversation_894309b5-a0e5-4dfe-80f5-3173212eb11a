digraph DeviceTree {
    "root";
    "root" -> "power";
    "power";
    "power" -> "bus600vbar";
    "bus600vbar";
    "bus600vbar" -> "tdinv";
    "tdinv";
    "tdinv" -> "tdmotor";
    "tdmotor";
    "tdmotor" -> "tdgearbox";
    "tdgearbox";
    "tdgearbox" -> "well1";
    "well1";
    "bus600vbar" -> "mp1_inv";
    "mp1_inv";
    "mp1_inv" -> "motor7";
    "motor7";
    "motor7" -> "mp1";
    "mp1";
    "mp1" -> "well1";
    "bus600vbar" -> "mp2_inv";
    "mp2_inv";
    "mp2_inv" -> "motor8";
    "motor8";
    "motor8" -> "mp2";
    "mp2";
    "mp2" -> "well1";
    "bus600vbar" -> "mp3_inv";
    "mp3_inv";
    "mp3_inv" -> "motor9";
    "motor9";
    "motor9" -> "mp3";
    "mp3";
    "mp3" -> "well1";
    "bus600vbar" -> "motor6";
    "motor6";
    "motor6" -> "blower2";
    "blower2";
    "bus600vbar" -> "invertor1";
    "invertor1";
    "invertor1" -> "motor1";
    "motor1";
    "motor1" -> "gearbox1";
    "gearbox1";
    "gearbox1" -> "drum";
    "drum";
    "drum" -> "hookload";
    "hookload";
    "hookload" -> "well1";
    "bus600vbar" -> "invertor2";
    "invertor2";
    "invertor2" -> "motor2";
    "motor2";
    "motor2" -> "gearbox2";
    "gearbox2";
    "gearbox2" -> "drum";
    "bus600vbar" -> "motor3";
    "motor3";
    "bus600vbar" -> "motor4";
    "motor4";
    "motor4" -> "blower1";
    "blower1";
    "bus600vbar" -> "motor5";
    "motor5";
    "motor5" -> "pump1";
    "pump1";
    "pump1" -> "cylinder1";
    "cylinder1";
    "pump1" -> "cylinder2";
    "cylinder2";
    "root" -> "bus600vbar";
}
