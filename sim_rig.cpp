#include <iostream>
#include <string>
#include <memory>
#include <thread>
#include <iomanip>
#include "AloLogger.h"
#include <omp.h>

// �豸ͷ�ļ�
#include "SimulationController.h"
#include "PowerSupply.h"
#include "Motor.h"
#include "Blower.h"
#include "Pump.h"
#include "Drum.h"
#include "Hookblock.h"
#include "Invertor.h"
#include "GearBox.h"
#include "HydralicCylinder.h"
#include "PassiveTorqueLoad.h"
#include "Well.h"
#include "EmptyDevice.h"
#include "Busbar.h"
#include "Breaker.h"
#include "Transformer.h"
#include "Rectifier.h"
#include "DynamicBrake.h"
#include "HydralicTong.h"
#include "ElectricCylinder.h"
#include "HydralicMotor.h"

using namespace std;
using namespace sim_rig;

// void test_omp() {
//	omp_set_num_threads(8); // ��ѡ��ǿ��8�߳�
// #pragma omp parallel for
//	for (int i = 0; i < 8; ++i) {
//		printf("Thread %d handles i=%d\n", omp_get_thread_num(), i);
//	}
// }

string get_motor_data(const std::shared_ptr<Motor> &motor)
{
	if (!motor)
	{
		return "������";
	}
	string data;
	data += motor->get_name();
	data += ":: ת��: " + to_string(motor->get_speed()) + " rpm, ";
	data += "Ť��: " + to_string(motor->get_torque()) + " Nm, ";
	data += "����: " + to_string(motor->get_power()) + " kW";
	return data;
};

int main()
{
	Logger::getInstance().setLogFile("log");
	Logger::getInstance().info("=====>����ʼ����...");

	auto sim_controller = make_shared<SimulationController>();

	auto root_device = sim_controller->create_device<EmptyDevice>("root");

	auto power = sim_controller->create_device<PowerSupply>("power", 600, 50);
	auto bus600vbar = sim_controller->create_device<Busbar>("bus600vbar");
	auto bus400vbar = sim_controller->create_device<Busbar>("bus400vbar");
	auto busdcbar1 = sim_controller->create_device<Busbar>("busdcbar1");
	auto busdcbar2 = sim_controller->create_device<Busbar>("busdcbar2");

	auto rectifier1 = sim_controller->create_device<Rectifier>("rectifier1", 4000, 600);
	auto rectifier2 = sim_controller->create_device<Rectifier>("rectifier2", 4000, 600);

	auto breaker1 = sim_controller->create_device<Breaker>("breaker1", 2000.0);
	auto breaker2 = sim_controller->create_device<Breaker>("breaker2", 2000.0);
	auto breaker3 = sim_controller->create_device<Breaker>("breaker3", 2000.0);
	auto breaker4 = sim_controller->create_device<Breaker>("breaker4", 2000.0);
	auto breaker5 = sim_controller->create_device<Breaker>("breaker5", 2000.0);

	auto dynamic_brake1 = sim_controller->create_device<DynamicBrake>("dynamic_brake1", 2.3, 1010, 970);
	auto dynamic_brake2 = sim_controller->create_device<DynamicBrake>("dynamic_brake2", 2.3, 1010, 970);

	auto transformer1 = sim_controller->create_device<Transformer>("transformer1", 1000.0, 4.0 / 6.0);

	auto motor1 = sim_controller->create_device<Motor>("dw_motor1", MotorParams{1000, 600, 1000, 50.5, 0.85, 300});
	auto motor2 = sim_controller->create_device<Motor>("dw_motor2", MotorParams{1000, 600, 1000, 50.5, 0.85, 300});
	auto motor3 = sim_controller->create_device<Motor>("motor3", MotorParams{1000, 600, 1000, 50.5, 0.85, 300});
	auto motor4 = sim_controller->create_device<Motor>("motor4", MotorParams{15, 380, 1470, 50, 0.85, 300});
	auto motor5 = sim_controller->create_device<Motor>("motor5", MotorParams{30, 380, 1470, 50, 0.85, 300});
	auto motor6 = sim_controller->create_device<Motor>("motor6", MotorParams{11, 380, 1470, 50, 0.85, 200});

	auto mt100 = sim_controller->create_device<Motor>("mt100", MotorParams{11, 380, 1470, 50, 0.85, 200});
	auto mt101 = sim_controller->create_device<Motor>("mt101", MotorParams{11, 380, 1470, 50, 0.85, 200});
	auto mt102 = sim_controller->create_device<Motor>("mt102", MotorParams{11, 380, 1470, 50, 0.85, 200});
	auto mt103 = sim_controller->create_device<Motor>("mt103", MotorParams{11, 380, 1470, 50, 0.85, 200});

	auto mt104 = sim_controller->create_device<Motor>("mt104", MotorParams{11, 380, 1470, 50, 0.85, 200});
	auto mt105 = sim_controller->create_device<Motor>("mt105", MotorParams{11, 380, 1470, 50, 0.85, 200});

	auto motor20 = sim_controller->create_device<Motor>("motor20", MotorParams{5, 380, 1470, 50, 0.85, 500});

	auto brake_pump_motor = sim_controller->create_device<Motor>("brake_pump_motor", MotorParams{15, 380, 1470, 50, 0.85, 500});
	auto brake_pump = sim_controller->create_device<Pump>("brake_pump", 60, 20, 1450, 1.0, true, 8.0);

	auto hdy_motor1 = sim_controller->create_device<HydralicMotor>("hdy_motor1", 50.0);

	// �ཬ��
	auto motor7 = sim_controller->create_device<Motor>("mp_motor7", MotorParams{1200, 600, 1000, 50.5, 0.85, 300});
	auto motor8 = sim_controller->create_device<Motor>("mp_motor8", MotorParams{1200, 600, 1000, 50.5, 0.85, 300});
	auto motor9 = sim_controller->create_device<Motor>("mp_motor9", MotorParams{1200, 600, 1000, 50.5, 0.85, 300});

	auto mp1 = sim_controller->create_pump("mp1", 23190.0, 1200, 120, 8.33, false, 0.0);
	auto mp2 = sim_controller->create_pump("mp2", 23190.0, 1200, 120, 8.33, false, 0.0);
	auto mp3 = sim_controller->create_pump("mp3", 23190.0, 1200, 120, 8.33, false, 0.0);

	auto mp1_inv = sim_controller->create_device<Invertor>("mp1_inv", 1000.0, 600.0);
	auto mp2_inv = sim_controller->create_device<Invertor>("mp2_inv", 1000.0, 600.0);
	auto mp3_inv = sim_controller->create_device<Invertor>("mp3_inv", 1000.0, 600.0);

	// ���������������䣬������������
	auto tdmotor = sim_controller->create_device<Motor>("tdmotor", MotorParams{600, 600, 714, 36.5, 0.85, 300});
	auto tdgearbox = sim_controller->create_device<GearBox>("tdgearbox", 10.0);
	auto tdinv = sim_controller->create_device<Invertor>("tdinv", 700.0, 600.0);

	auto blower1 = sim_controller->create_device<Blower>("blower1", 1500, 11.0, 10.0);
	auto blower2 = sim_controller->create_device<PassiveTorqueLoad>("blower2",
																	std::make_unique<BlowerModel>(0.00003));

	auto pump1 = sim_controller->create_device<Pump>("pump1", 100.0, 20, 1450, 1.0, false, 0);
	auto drum = sim_controller->create_device<Drum>("drum", 1.2);
	auto hook = sim_controller->create_device<Hookblock>("hookblock", 10.0);
	auto invertor1 = sim_controller->create_device<Invertor>("invertor1", 1000.0, 600.0);
	auto invertor2 = sim_controller->create_device<Invertor>("invertor2", 1000.0, 600.0);
	// invertor2->set_pid_enable(false);

	auto gearbox1 = sim_controller->create_device<GearBox>("dw_gearbox1", 10.0);
	auto gearbox2 = sim_controller->create_device<GearBox>("dw_gearbox2", 10.0);
	auto gearbox3 = sim_controller->create_device<GearBox>("gearbox3", 10.0);

	auto nc_cylinder = sim_controller->create_device<HydralicCylinder>("nc_cylinder1", 0.25, 0.4);
	auto no_cylinder = sim_controller->create_device<HydralicCylinder>("no_cylinder2", 0.25, 0.4);

	auto nobraketon = sim_controller->create_device<HydralicTong>("btong1", 3, false);
	auto ncbraketon = sim_controller->create_device<HydralicTong>("btong2", 3, true);

	auto cylinder1 = sim_controller->create_device<HydralicCylinder>("cylinder1", 0.1, 2.0);
	auto cylinder2 = sim_controller->create_device<HydralicCylinder>("cylinder2", 0.15, 2.0);

	auto well1 = sim_controller->create_device<Well>("well1", 1.0);

	sim_controller->set_root(root_device);

	sim_controller->connect_devices(power, root_device);

	sim_controller->connect_devices(breaker1, power);
	sim_controller->connect_devices(bus600vbar, breaker1);

	sim_controller->connect_devices(transformer1, bus600vbar);
	sim_controller->connect_devices(breaker2, transformer1);
	sim_controller->connect_devices(bus400vbar, breaker2);

	sim_controller->connect_devices(breaker3, bus600vbar);
	sim_controller->connect_devices(rectifier1, breaker3);
	sim_controller->connect_devices(rectifier2, breaker5);
	sim_controller->connect_devices(breaker5, bus600vbar);

	sim_controller->connect_devices(busdcbar1, rectifier1);
	sim_controller->connect_devices(busdcbar2, rectifier2);

	sim_controller->connect_devices(dynamic_brake1, busdcbar1);
	sim_controller->connect_devices(dynamic_brake2, busdcbar2);

	sim_controller->connect_devices(motor20, bus400vbar);

	sim_controller->connect_devices(tdgearbox, tdmotor);
	sim_controller->connect_devices(tdmotor, tdinv);
	sim_controller->connect_devices(tdinv, busdcbar1);
	sim_controller->connect_devices(well1, tdgearbox);
	sim_controller->connect_devices(blower2, motor6);

	sim_controller->connect_devices(motor7, mp1_inv);
	sim_controller->connect_devices(motor8, mp2_inv);
	sim_controller->connect_devices(motor9, mp3_inv);

	sim_controller->connect_devices(mp1, motor7);
	sim_controller->connect_devices(mp2, motor8);
	sim_controller->connect_devices(mp3, motor9);

	sim_controller->connect_devices(mp1_inv, busdcbar2);
	sim_controller->connect_devices(mp2_inv, busdcbar2);
	sim_controller->connect_devices(mp3_inv, busdcbar2);

	sim_controller->connect_devices(motor6, bus400vbar);

	sim_controller->connect_devices(mt100, bus400vbar);
	sim_controller->connect_devices(mt101, bus400vbar);
	sim_controller->connect_devices(mt102, bus400vbar);
	sim_controller->connect_devices(mt103, bus400vbar);

	sim_controller->connect_devices(mt104, bus400vbar);
	sim_controller->connect_devices(mt105, bus400vbar);

	sim_controller->connect_devices(well1, hook);

	sim_controller->connect_devices(hook, drum);
	sim_controller->connect_devices(drum, gearbox1);
	sim_controller->connect_devices(drum, gearbox2);

	sim_controller->connect_devices(gearbox1, motor1);
	sim_controller->connect_devices(gearbox2, motor2);

	sim_controller->connect_devices(motor1, invertor1);
	sim_controller->connect_devices(motor2, invertor2);

	sim_controller->connect_devices(invertor1, busdcbar1);
	sim_controller->connect_devices(invertor2, busdcbar1);

	sim_controller->connect_devices(well1, mp1);
	sim_controller->connect_devices(well1, mp2);
	sim_controller->connect_devices(well1, mp3);

	sim_controller->connect_devices(brake_pump_motor, bus400vbar);
	sim_controller->connect_devices(brake_pump, brake_pump_motor);
	sim_controller->connect_devices(nc_cylinder, brake_pump);
	sim_controller->connect_devices(no_cylinder, brake_pump);

	sim_controller->connect_devices(nobraketon, no_cylinder);
	sim_controller->connect_devices(ncbraketon, nc_cylinder);

	sim_controller->connect_devices(drum, nobraketon);
	sim_controller->connect_devices(drum, ncbraketon);

	nc_cylinder->set_valve(100);
	no_cylinder->set_valve(0);
	// for test....
	// std::vector<std::shared_ptr<BaseDevice>> test_devices;
	well1->set_pipe_count(450);

	invertor1->set_speed(200);
	invertor2->set_speed(0);
	// invertor1->set_pid_enable(false);
	// invertor2->set_pid_enable(false);
	invertor1->connect_partner(invertor2);
	invertor2->set_mode(Invertor::ControlMode::FLLOWER); // ������Ƶ�����ӿ���ģʽ,�ӻ�ת���趨����Ч
	tdinv->set_speed(1000);
	mp1_inv->set_speed(30);
	mp2_inv->set_speed(500);
	mp3_inv->set_speed(400);

	sim_controller->connect_devices(motor3, bus400vbar);

	sim_controller->connect_devices(blower1, motor4);
	sim_controller->connect_devices(motor4, bus400vbar);

	sim_controller->connect_devices(pump1, motor5);
	sim_controller->connect_devices(motor5, bus400vbar);

	sim_controller->connect_devices(hdy_motor1, pump1);

	sim_controller->connect_devices(cylinder1, pump1);
	cylinder1->set_valve(50);
	cylinder1->set_load(53000.0);

	sim_controller->connect_devices(cylinder2, pump1);
	cylinder2->set_valve(20);
	cylinder2->set_load(98000.0);

	well1->set_well_depth(1000.0);
	well1->set_bit_pos(1000.0);

	well1->set_flow_gain_loss(-180);

	// motor1->set_status(DeviceStatus::STOP);
	// motor2->set_status(DeviceStatus::STOP);
	// motor3->set_status(DeviceStatus::STOP);
	// motor7->set_status(DeviceStatus::STOP);
	// motor8->set_status(DeviceStatus::STOP);
	// motor9->set_status(DeviceStatus::STOP);
	// tdmotor->set_status(DeviceStatus::OFF);

	// for test!.....
	// for (int i = 0; i < 250; i++)
	//{
	//	auto bl = sim_controller->create_device<Blower>("test_blower" + std::to_string(i), 1500, 12.0, 10.0);
	//	auto mt = sim_controller->create_device<Motor>("test_motor" + std::to_string(i), 11, 1470.0, 50.0, 1000.0);
	//	auto gb = sim_controller->create_device<GearBox>("test_gearbox" + std::to_string(i), 10.0);
	//	auto iv = sim_controller->create_device<Invertor>("test_invertor" + std::to_string(i), 1000.0, 600.0);
	//	sim_controller->connect_devices(bl, gb);
	//	sim_controller->connect_devices(gb, mt);
	//	sim_controller->connect_devices(mt, iv);
	//	sim_controller->connect_devices(iv, power);
	// }

	// ��ʾ�豸��
	std::cout << "�豸���ṹ:" << std::endl;
	std::cout << sim_controller->get_device_tree() << std::endl;
	system("pause");
	// return 0;
	Logger::getInstance().info("�豸���ṹ: " + sim_controller->get_device_tree());

	root_device->print_tree_to_dot("dev_tree.dot");
	root_device->print_tree_to_file("dev_tree.txt");

	root_device->print_tree("");

	system("pause");

	// 启动HTTP API服务器
	if (sim_controller->start_http_api_server(8080))
	{
		Logger::getInstance().info("HTTP API服务器启动成功，端口: 8080");
		std::cout << "HTTP API服务器已启动，访问 http://localhost:8080 查看API文档" << std::endl;
	}
	else
	{
		Logger::getInstance().error("HTTP API服务器启动失败");
	}

	//   模拟启动
	sim_controller->start_simulation(false);
	Logger::getInstance().info("开始仿真...");
	// system("pause");
	//  ��������һ��ʱ��
	double show_time = 0.0;
	double total_time = 0.0;
	auto start_time = std::chrono::steady_clock::now();

	motor1->set_value<bool>("running", true);
	motor1->set_value<double>("test", 888.0);

	// system("pause");

	while (true) // ����x����
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // ÿ500ms���һ��

		// ��ȡ����ͳ��
		auto stats = sim_controller->get_performance_stats();
		show_time += 0.2;
		total_time += 0.2;

		// ��ӡ״̬��Ϣ
		std::cout << "\033[2J\033[H"; // ����
		std::cout << "-----------------����������Ϣ---------------------" << std::endl;
		std::cout << std::fixed << std::setprecision(6);
		std::cout << "����ʱ��: " << total_time << " s" << std::endl;
		std::cout << "�豸����: " << sim_controller->get_device_count() << std::endl;
		std::cout << "ƽ��֡ʱ��: " << stats.avg_frame_time * 1000 << " ms" << std::endl;
		std::cout << "��С֡ʱ��: " << stats.min_frame_time * 1000 << " ms" << std::endl;
		std::cout << "���֡ʱ��: " << stats.max_frame_time * 1000 << " ms" << std::endl;
		std::cout << "��ǰFPS: " << stats.fps << std::endl;
		std::cout << "��֡��: " << stats.total_frames << std::endl;

		cout << "======================�豸��Ϣ=========================" << endl;
		std::cout << std::fixed << std::setprecision(6) << endl;
		cout << "Power: " << power->get_power() << " kW," << power->get_current() << " A" << endl;
		cout << "blower1: " << blower1->get_speed() << " RPM, " << blower1->get_torque() << " Nm" << endl;
		cout << "��ѹ���أ�" << blower1->get_blower_switch() << endl;
		cout << get_motor_data(motor1) << endl;
		cout << get_motor_data(motor2) << endl;
		cout << get_motor_data(motor3) << endl;
		cout << get_motor_data(motor4) << endl;
		cout << get_motor_data(motor5) << endl;
		cout << get_motor_data(motor6) << endl;
		cout << get_motor_data(motor7) << endl;
		cout << get_motor_data(motor8) << endl;
		cout << get_motor_data(motor9) << endl;
		cout << get_motor_data(motor20) << endl;
		cout << get_motor_data(brake_pump_motor) << endl;
		cout << get_motor_data(tdmotor) << endl;
		cout << "Drum: " << drum->get_speed() << " rpm, " << drum->get_torque() << " Nm" << endl;
		cout << "Load: weight=" << hook->get_hook_load() / 1000.0 << " t, speed=" << hook->get_hook_speed() << " m/s" << endl;
		cout << "pump1: pressure=" << pump1->get_pressure() << " MPa, flow=" << pump1->get_displacement() << " L/min" << endl;
		cout << "cyliner1: speed=" << cylinder1->get_speed() << " m/s, length=" << cylinder1->get_strength() << " m" << endl;
		cout << "cyliner2: speed=" << cylinder2->get_speed() << " m/s, length=" << cylinder2->get_strength() << " m" << endl;
		cout << "gear1: speed=" << gearbox1->get_speed() << " rpm" << endl;
		cout << "well: depth=" << well1->get_well_depth() << " m ,bit=" << well1->get_bit_pos() << " rpm, out_flow=" << well1->get_flowrate() << " L/min" << endl;
		cout << "wob=" << well1->get_wob() << endl;
		cout << "mp1: spm=" << mp1->get_speed() << " rpm, pressure=" << mp1->get_pressure() << " MPa" << endl;

		cout << "bus bar dc_voltage=" << busdcbar1->get_volt() << endl;
		cout << "rectifier1:volt=" << rectifier1->get_volt() << " v, power=" << rectifier2->get_power() << " kw" << endl;
		cout << "rectifier2:volt=" << rectifier2->get_volt() << " v, power=" << rectifier2->get_power() << " kw" << endl;
		cout << "�ܺ��ƶ���volt=" << dynamic_brake1->get_volt() << " v, power=" << dynamic_brake1->get_power() << " kw, current=" << dynamic_brake1->get_current() << " A" << endl;
		cout << "status=" << invertor1->get_status_str() << endl;

		cout << "+++++++++++++debug+++++++++++++" << endl;

		cout << "mp1 motor=" << motor7->get_debug_value("onoff") << endl;
		cout << "mp1 inv status=" << mp1_inv->get_debug_value("onoff") << endl;

		auto v1 = motor1->get_value<double>("speed2");
		auto v2 = motor1->get_value<double>("torque");
		auto v3 = motor1->get_value<bool>("running") ? "running" : "stop";
		auto v4 = motor1->get_value<double>("test");

		cout << "*************************************" << endl;
		cout << "motor1:speed=" << v1 << ",torque=" << v2 << ",running=" << v3 << ",test=" << v4 << endl;

		auto v5 = motor2->get_value<DeviceStatus>("status");

		cout << "motor2:status=" << (int)v5 << endl;

		double gang = cylinder1->get_strength();
		if (gang > 1.7)
		{
			cylinder1->set_valve(-30);
			cylinder2->set_valve(-30);
		}
		else if (gang < 0.2)
		{
			cylinder1->set_valve(50);
			cylinder2->set_valve(40);
		}

		std::cout << "========end: " << stats.avg_frame_time * 1000 << "ms, total:" << total_time << " s=======================" << std::endl;
	}
	// ========== ���Ľ׶Σ�ֹͣ���� ==========
	std::cout << "=== ֹͣ���� ===" << std::endl;
	sim_controller->stop_simulation();
	Logger::getInstance().info("=== ֹͣ���� ===");
}