<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1434pt" height="980pt" viewBox="0.00 0.00 1433.61 980.00">
<g id="graph0" class="graph" transform="translate(128.50937236155704,619.8876112787505) scale(0.6708211081841297)" data-name="DeviceTree">

<polygon fill="white" stroke="none" points="-4,4 -4,-976 1429.61,-976 1429.61,4 -4,4"/>
<!-- root -->
<g id="node1" class="node" pointer-events="visible" data-name="root">

<ellipse fill="none" stroke="black" cx="710.5" cy="-954" rx="27" ry="18"/>
<text text-anchor="middle" x="710.5" y="-949.8" font-family="Times,serif" font-size="14.00">root</text>
</g>
<!-- power -->
<g id="node2" class="node" pointer-events="visible" data-name="power">

<ellipse fill="none" stroke="black" cx="710.5" cy="-882" rx="35.34" ry="18"/>
<text text-anchor="middle" x="710.5" y="-877.8" font-family="Times,serif" font-size="14.00">power</text>
</g>
<!-- root&#45;&gt;power -->
<g id="edge1" class="edge" data-name="root-&gt;power">

<path fill="none" stroke="black" d="M710.5,-935.7C710.5,-928.41 710.5,-919.73 710.5,-911.54"/>
<polygon fill="black" stroke="black" points="714,-911.62 710.5,-901.62 707,-911.62 714,-911.62"/>
</g>
<!-- breaker1 -->
<g id="node3" class="node" pointer-events="visible" data-name="breaker1">

<ellipse fill="none" stroke="black" cx="710.5" cy="-810" rx="44.63" ry="18"/>
<text text-anchor="middle" x="710.5" y="-805.8" font-family="Times,serif" font-size="14.00">breaker1</text>
</g>
<!-- power&#45;&gt;breaker1 -->
<g id="edge2" class="edge" data-name="power-&gt;breaker1">

<path fill="none" stroke="black" d="M710.5,-863.7C710.5,-856.41 710.5,-847.73 710.5,-839.54"/>
<polygon fill="black" stroke="black" points="714,-839.62 710.5,-829.62 707,-839.62 714,-839.62"/>
</g>
<!-- bus600vbar -->
<g id="node4" class="node" pointer-events="visible" data-name="bus600vbar">

<ellipse fill="none" stroke="black" cx="710.5" cy="-738" rx="56.19" ry="18"/>
<text text-anchor="middle" x="710.5" y="-733.8" font-family="Times,serif" font-size="14.00">bus600vbar</text>
</g>
<!-- breaker1&#45;&gt;bus600vbar -->
<g id="edge3" class="edge" data-name="breaker1-&gt;bus600vbar">

<path fill="none" stroke="black" d="M710.5,-791.7C710.5,-784.41 710.5,-775.73 710.5,-767.54"/>
<polygon fill="black" stroke="black" points="714,-767.62 710.5,-757.62 707,-767.62 714,-767.62"/>
</g>
<!-- transformer1 -->
<g id="node5" class="node" pointer-events="visible" data-name="transformer1">

<ellipse fill="none" stroke="black" cx="648.5" cy="-666" rx="60.82" ry="18"/>
<text text-anchor="middle" x="648.5" y="-661.8" font-family="Times,serif" font-size="14.00">transformer1</text>
</g>
<!-- bus600vbar&#45;&gt;transformer1 -->
<g id="edge4" class="edge" data-name="bus600vbar-&gt;transformer1">

<path fill="none" stroke="black" d="M695.81,-720.41C688.32,-711.95 679.06,-701.49 670.72,-692.08"/>
<polygon fill="black" stroke="black" points="673.35,-689.77 664.1,-684.6 668.11,-694.41 673.35,-689.77"/>
</g>
<!-- breaker3 -->
<g id="node27" class="node" pointer-events="visible" data-name="breaker3">

<ellipse fill="none" stroke="black" cx="834.5" cy="-666" rx="44.63" ry="18"/>
<text text-anchor="middle" x="834.5" y="-661.8" font-family="Times,serif" font-size="14.00">breaker3</text>
</g>
<!-- bus600vbar&#45;&gt;breaker3 -->
<g id="edge27" class="edge" data-name="bus600vbar-&gt;breaker3">

<path fill="none" stroke="black" d="M737.41,-721.81C755.61,-711.54 779.82,-697.87 799.51,-686.75"/>
<polygon fill="black" stroke="black" points="801.04,-689.91 808.02,-681.95 797.59,-683.82 801.04,-689.91"/>
</g>
<!-- breaker2 -->
<g id="node6" class="node" pointer-events="visible" data-name="breaker2">

<ellipse fill="none" stroke="black" cx="523.5" cy="-594" rx="44.63" ry="18"/>
<text text-anchor="middle" x="523.5" y="-589.8" font-family="Times,serif" font-size="14.00">breaker2</text>
</g>
<!-- transformer1&#45;&gt;breaker2 -->
<g id="edge5" class="edge" data-name="transformer1-&gt;breaker2">

<path fill="none" stroke="black" d="M621.07,-649.64C602.71,-639.36 578.38,-625.73 558.61,-614.66"/>
<polygon fill="black" stroke="black" points="560.5,-611.71 550.07,-609.87 557.08,-617.81 560.5,-611.71"/>
</g>
<!-- bus400vbar -->
<g id="node7" class="node" pointer-events="visible" data-name="bus400vbar">

<ellipse fill="none" stroke="black" cx="274.5" cy="-522" rx="56.19" ry="18"/>
<text text-anchor="middle" x="274.5" y="-517.8" font-family="Times,serif" font-size="14.00">bus400vbar</text>
</g>
<!-- breaker2&#45;&gt;bus400vbar -->
<g id="edge6" class="edge" data-name="breaker2-&gt;bus400vbar">

<path fill="none" stroke="black" d="M487.56,-582.9C445.33,-571.02 374.91,-551.23 326.67,-537.67"/>
<polygon fill="black" stroke="black" points="327.86,-534.36 317.29,-535.03 325.96,-541.1 327.86,-534.36"/>
</g>
<!-- motor6 -->
<g id="node8" class="node" pointer-events="visible" data-name="motor6">

<ellipse fill="none" stroke="black" cx="339.5" cy="-450" rx="38.86" ry="18"/>
<text text-anchor="middle" x="339.5" y="-445.8" font-family="Times,serif" font-size="14.00">motor6</text>
</g>
<!-- bus400vbar&#45;&gt;motor6 -->
<g id="edge7" class="edge" data-name="bus400vbar-&gt;motor6">

<path fill="none" stroke="black" d="M289.91,-504.41C297.96,-495.74 307.96,-484.97 316.87,-475.38"/>
<polygon fill="black" stroke="black" points="319.39,-477.81 323.63,-468.1 314.26,-473.04 319.39,-477.81"/>
</g>
<!-- brake_pump_motor -->
<g id="node10" class="node" pointer-events="visible" data-name="brake_pump_motor">

<ellipse fill="none" stroke="black" cx="483.5" cy="-450" rx="87.41" ry="18"/>
<text text-anchor="middle" x="483.5" y="-445.8" font-family="Times,serif" font-size="14.00">brake_pump_motor</text>
</g>
<!-- bus400vbar&#45;&gt;brake_pump_motor -->
<g id="edge9" class="edge" data-name="bus400vbar-&gt;brake_pump_motor">

<path fill="none" stroke="black" d="M312.35,-508.32C344.79,-497.46 391.94,-481.67 428.54,-469.41"/>
<polygon fill="black" stroke="black" points="429.24,-472.87 437.61,-466.37 427.02,-466.23 429.24,-472.87"/>
</g>
<!-- motor3 -->
<g id="node19" class="node" pointer-events="visible" data-name="motor3">

<ellipse fill="none" stroke="black" cx="147.5" cy="-450" rx="38.86" ry="18"/>
<text text-anchor="middle" x="147.5" y="-445.8" font-family="Times,serif" font-size="14.00">motor3</text>
</g>
<!-- bus400vbar&#45;&gt;motor3 -->
<g id="edge19" class="edge" data-name="bus400vbar-&gt;motor3">

<path fill="none" stroke="black" d="M247.26,-505.98C228.06,-495.4 202.2,-481.15 181.62,-469.81"/>
<polygon fill="black" stroke="black" points="183.48,-466.83 173.03,-465.07 180.1,-472.96 183.48,-466.83"/>
</g>
<!-- motor4 -->
<g id="node20" class="node" pointer-events="visible" data-name="motor4">

<ellipse fill="none" stroke="black" cx="51.5" cy="-450" rx="38.86" ry="18"/>
<text text-anchor="middle" x="51.5" y="-445.8" font-family="Times,serif" font-size="14.00">motor4</text>
</g>
<!-- bus400vbar&#45;&gt;motor4 -->
<g id="edge20" class="edge" data-name="bus400vbar-&gt;motor4">

<path fill="none" stroke="black" d="M233.67,-509.22C198.1,-498.81 145.2,-483 99.5,-468 97.12,-467.22 94.67,-466.4 92.2,-465.56"/>
<polygon fill="black" stroke="black" points="93.41,-462.28 82.82,-462.32 91.13,-468.89 93.41,-462.28"/>
</g>
<!-- motor5 -->
<g id="node22" class="node" pointer-events="visible" data-name="motor5">

<ellipse fill="none" stroke="black" cx="243.5" cy="-450" rx="38.86" ry="18"/>
<text text-anchor="middle" x="243.5" y="-445.8" font-family="Times,serif" font-size="14.00">motor5</text>
</g>
<!-- bus400vbar&#45;&gt;motor5 -->
<g id="edge22" class="edge" data-name="bus400vbar-&gt;motor5">

<path fill="none" stroke="black" d="M266.84,-503.7C263.46,-496.07 259.41,-486.92 255.64,-478.4"/>
<polygon fill="black" stroke="black" points="258.94,-477.21 251.69,-469.49 252.54,-480.05 258.94,-477.21"/>
</g>
<!-- blower2 -->
<g id="node9" class="node" pointer-events="visible" data-name="blower2">

<ellipse fill="none" stroke="black" cx="339.5" cy="-378" rx="42.86" ry="18"/>
<text text-anchor="middle" x="339.5" y="-373.8" font-family="Times,serif" font-size="14.00">blower2</text>
</g>
<!-- motor6&#45;&gt;blower2 -->
<g id="edge8" class="edge" data-name="motor6-&gt;blower2">

<path fill="none" stroke="black" d="M339.5,-431.7C339.5,-424.41 339.5,-415.73 339.5,-407.54"/>
<polygon fill="black" stroke="black" points="343,-407.62 339.5,-397.62 336,-407.62 343,-407.62"/>
</g>
<!-- brake_pump -->
<g id="node11" class="node" pointer-events="visible" data-name="brake_pump">

<ellipse fill="none" stroke="black" cx="483.5" cy="-378" rx="59.11" ry="18"/>
<text text-anchor="middle" x="483.5" y="-373.8" font-family="Times,serif" font-size="14.00">brake_pump</text>
</g>
<!-- brake_pump_motor&#45;&gt;brake_pump -->
<g id="edge10" class="edge" data-name="brake_pump_motor-&gt;brake_pump">

<path fill="none" stroke="black" d="M483.5,-431.7C483.5,-424.41 483.5,-415.73 483.5,-407.54"/>
<polygon fill="black" stroke="black" points="487,-407.62 483.5,-397.62 480,-407.62 487,-407.62"/>
</g>
<!-- nc_cylinder1 -->
<g id="node12" class="node" pointer-events="visible" data-name="nc_cylinder1">

<ellipse fill="none" stroke="black" cx="422.5" cy="-306" rx="61.4" ry="18"/>
<text text-anchor="middle" x="422.5" y="-301.8" font-family="Times,serif" font-size="14.00">nc_cylinder1</text>
</g>
<!-- brake_pump&#45;&gt;nc_cylinder1 -->
<g id="edge11" class="edge" data-name="brake_pump-&gt;nc_cylinder1">

<path fill="none" stroke="black" d="M469.05,-360.41C461.79,-352.08 452.83,-341.8 444.72,-332.49"/>
<polygon fill="black" stroke="black" points="447.48,-330.34 438.28,-325.1 442.21,-334.94 447.48,-330.34"/>
</g>
<!-- no_cylinder2 -->
<g id="node17" class="node" pointer-events="visible" data-name="no_cylinder2">

<ellipse fill="none" stroke="black" cx="563.5" cy="-306" rx="61.98" ry="18"/>
<text text-anchor="middle" x="563.5" y="-301.8" font-family="Times,serif" font-size="14.00">no_cylinder2</text>
</g>
<!-- brake_pump&#45;&gt;no_cylinder2 -->
<g id="edge16" class="edge" data-name="brake_pump-&gt;no_cylinder2">

<path fill="none" stroke="black" d="M502.06,-360.76C512.18,-351.91 524.91,-340.77 536.15,-330.93"/>
<polygon fill="black" stroke="black" points="538.41,-333.61 543.63,-324.39 533.8,-328.34 538.41,-333.61"/>
</g>
<!-- btong2 -->
<g id="node13" class="node" pointer-events="visible" data-name="btong2">

<ellipse fill="none" stroke="black" cx="446.5" cy="-234" rx="37.72" ry="18"/>
<text text-anchor="middle" x="446.5" y="-229.8" font-family="Times,serif" font-size="14.00">btong2</text>
</g>
<!-- nc_cylinder1&#45;&gt;btong2 -->
<g id="edge12" class="edge" data-name="nc_cylinder1-&gt;btong2">

<path fill="none" stroke="black" d="M428.44,-287.7C430.99,-280.24 434.05,-271.32 436.92,-262.97"/>
<polygon fill="black" stroke="black" points="440.22,-264.13 440.15,-253.54 433.6,-261.86 440.22,-264.13"/>
</g>
<!-- drum -->
<g id="node14" class="node" pointer-events="visible" data-name="drum">

<ellipse fill="none" stroke="black" cx="683.5" cy="-162" rx="31.34" ry="18"/>
<text text-anchor="middle" x="683.5" y="-157.8" font-family="Times,serif" font-size="14.00">drum</text>
</g>
<!-- btong2&#45;&gt;drum -->
<g id="edge13" class="edge" data-name="btong2-&gt;drum">

<path fill="none" stroke="black" d="M477.98,-223.7C521.13,-210.96 598.61,-188.07 645.12,-174.34"/>
<polygon fill="black" stroke="black" points="645.91,-177.75 654.51,-171.57 643.92,-171.04 645.91,-177.75"/>
</g>
<!-- hookblock -->
<g id="node15" class="node" pointer-events="visible" data-name="hookblock">

<ellipse fill="none" stroke="black" cx="788.5" cy="-90" rx="51.61" ry="18"/>
<text text-anchor="middle" x="788.5" y="-85.8" font-family="Times,serif" font-size="14.00">hookblock</text>
</g>
<!-- drum&#45;&gt;hookblock -->
<g id="edge14" class="edge" data-name="drum-&gt;hookblock">

<path fill="none" stroke="black" d="M703.25,-147.83C718.19,-137.88 739.02,-123.99 756.35,-112.44"/>
<polygon fill="black" stroke="black" points="757.91,-115.6 764.29,-107.14 754.03,-109.78 757.91,-115.6"/>
</g>
<!-- well1 -->
<g id="node16" class="node" pointer-events="visible" data-name="well1">

<ellipse fill="none" stroke="black" cx="1123.5" cy="-18" rx="32.48" ry="18"/>
<text text-anchor="middle" x="1123.5" y="-13.8" font-family="Times,serif" font-size="14.00">well1</text>
</g>
<!-- hookblock&#45;&gt;well1 -->
<g id="edge15" class="edge" data-name="hookblock-&gt;well1">

<path fill="none" stroke="black" d="M831.96,-79.92C896.96,-66.34 1018.65,-40.91 1082.31,-27.61"/>
<polygon fill="black" stroke="black" points="1082.94,-31.05 1092.01,-25.58 1081.51,-24.2 1082.94,-31.05"/>
</g>
<!-- btong1 -->
<g id="node18" class="node" pointer-events="visible" data-name="btong1">

<ellipse fill="none" stroke="black" cx="563.5" cy="-234" rx="37.72" ry="18"/>
<text text-anchor="middle" x="563.5" y="-229.8" font-family="Times,serif" font-size="14.00">btong1</text>
</g>
<!-- no_cylinder2&#45;&gt;btong1 -->
<g id="edge17" class="edge" data-name="no_cylinder2-&gt;btong1">

<path fill="none" stroke="black" d="M563.5,-287.7C563.5,-280.41 563.5,-271.73 563.5,-263.54"/>
<polygon fill="black" stroke="black" points="567,-263.62 563.5,-253.62 560,-263.62 567,-263.62"/>
</g>
<!-- btong1&#45;&gt;drum -->
<g id="edge18" class="edge" data-name="btong1-&gt;drum">

<path fill="none" stroke="black" d="M586.64,-219.5C605.29,-208.63 631.68,-193.23 652.21,-181.25"/>
<polygon fill="black" stroke="black" points="653.87,-184.34 660.74,-176.28 650.34,-178.29 653.87,-184.34"/>
</g>
<!-- blower1 -->
<g id="node21" class="node" pointer-events="visible" data-name="blower1">

<ellipse fill="none" stroke="black" cx="51.5" cy="-378" rx="42.86" ry="18"/>
<text text-anchor="middle" x="51.5" y="-373.8" font-family="Times,serif" font-size="14.00">blower1</text>
</g>
<!-- motor4&#45;&gt;blower1 -->
<g id="edge21" class="edge" data-name="motor4-&gt;blower1">

<path fill="none" stroke="black" d="M51.5,-431.7C51.5,-424.41 51.5,-415.73 51.5,-407.54"/>
<polygon fill="black" stroke="black" points="55,-407.62 51.5,-397.62 48,-407.62 55,-407.62"/>
</g>
<!-- pump1 -->
<g id="node23" class="node" pointer-events="visible" data-name="pump1">

<ellipse fill="none" stroke="black" cx="191.5" cy="-378" rx="37.72" ry="18"/>
<text text-anchor="middle" x="191.5" y="-373.8" font-family="Times,serif" font-size="14.00">pump1</text>
</g>
<!-- motor5&#45;&gt;pump1 -->
<g id="edge23" class="edge" data-name="motor5-&gt;pump1">

<path fill="none" stroke="black" d="M231.44,-432.76C225.14,-424.28 217.29,-413.71 210.22,-404.2"/>
<polygon fill="black" stroke="black" points="213.18,-402.3 204.4,-396.36 207.56,-406.48 213.18,-402.3"/>
</g>
<!-- cylinder1 -->
<g id="node25" class="node" pointer-events="visible" data-name="cylinder1">

<ellipse fill="none" stroke="black" cx="295.5" cy="-306" rx="47.51" ry="18"/>
<text text-anchor="middle" x="295.5" y="-301.8" font-family="Times,serif" font-size="14.00">cylinder1</text>
</g><g id="node24" class="node" pointer-events="visible" data-name="hdy_motor1" data-comment="cylinder1">

<ellipse fill="none" stroke="black" cx="171.5" cy="-306" rx="58.49" ry="18" style=""/>
<text text-anchor="middle" x="171.5" y="-301.8" font-family="Times,serif" font-size="14.00" style="">hdy_motor1</text>
</g>
<!-- pump1&#45;&gt;cylinder1 -->
<g id="edge25" class="edge" data-name="pump1-&gt;cylinder1">

<path fill="none" stroke="black" d="M212.8,-362.67C227.41,-352.84 247.05,-339.62 263.52,-328.53"/>
<polygon fill="black" stroke="black" points="265.2,-331.62 271.54,-323.13 261.29,-325.81 265.2,-331.62"/>
</g><g id="edge24" class="edge" data-name="pump1-&gt;hdy_motor1" data-comment="pump1-&gt;cylinder1">

<path fill="none" stroke="black" d="M186.56,-359.7C184.43,-352.24 181.88,-343.32 179.49,-334.97"/>
<polygon fill="black" stroke="black" points="182.92,-334.21 176.81,-325.56 176.19,-336.14 182.92,-334.21"/>
</g>
<!-- cylinder2 -->
<g id="node26" class="node" pointer-events="visible" data-name="cylinder2">

<ellipse fill="none" stroke="black" cx="47.5" cy="-306" rx="47.51" ry="18"/>
<text text-anchor="middle" x="47.5" y="-301.8" font-family="Times,serif" font-size="14.00">cylinder2</text>
</g>
<!-- pump1&#45;&gt;cylinder2 -->
<g id="edge26" class="edge" data-name="pump1-&gt;cylinder2">

<path fill="none" stroke="black" d="M165.76,-364.49C143.68,-353.75 111.52,-338.12 86.31,-325.87"/>
<polygon fill="black" stroke="black" points="87.87,-322.73 77.35,-321.51 84.81,-329.03 87.87,-322.73"/>
</g>
<!-- rectifier1 -->
<g id="node28" class="node" pointer-events="visible" data-name="rectifier1">

<ellipse fill="none" stroke="black" cx="873.5" cy="-594" rx="46.34" ry="18"/>
<text text-anchor="middle" x="873.5" y="-589.8" font-family="Times,serif" font-size="14.00">rectifier1</text>
</g>
<!-- breaker3&#45;&gt;rectifier1 -->
<g id="edge28" class="edge" data-name="breaker3-&gt;rectifier1">

<path fill="none" stroke="black" d="M843.95,-648.05C848.35,-640.14 853.7,-630.54 858.64,-621.69"/>
<polygon fill="black" stroke="black" points="861.6,-623.55 863.41,-613.11 855.49,-620.14 861.6,-623.55"/>
</g>
<!-- busdcbar -->
<g id="node29" class="node" pointer-events="visible" data-name="busdcbar">

<ellipse fill="none" stroke="black" cx="972.5" cy="-450" rx="45.79" ry="18"/>
<text text-anchor="middle" x="972.5" y="-445.8" font-family="Times,serif" font-size="14.00">busdcbar</text>
</g>
<!-- rectifier1&#45;&gt;busdcbar -->
<g id="edge29" class="edge" data-name="rectifier1-&gt;busdcbar">

<path fill="none" stroke="black" d="M885.25,-576.15C902.37,-551.6 934.32,-505.77 954.44,-476.91"/>
<polygon fill="black" stroke="black" points="957.06,-479.27 959.91,-469.07 951.32,-475.27 957.06,-479.27"/>
</g>
<!-- dynamic_brake1 -->
<g id="node30" class="node" pointer-events="visible" data-name="dynamic_brake1">

<ellipse fill="none" stroke="black" cx="1042.5" cy="-378" rx="75.28" ry="18"/>
<text text-anchor="middle" x="1042.5" y="-373.8" font-family="Times,serif" font-size="14.00">dynamic_brake1</text>
</g>
<!-- busdcbar&#45;&gt;dynamic_brake1 -->
<g id="edge30" class="edge" data-name="busdcbar-&gt;dynamic_brake1">

<path fill="none" stroke="black" d="M988.74,-432.76C997.36,-424.14 1008.14,-413.36 1017.78,-403.73"/>
<polygon fill="black" stroke="black" points="1020.2,-406.25 1024.8,-396.7 1015.26,-401.3 1020.2,-406.25"/>
</g>
<!-- tdinv -->
<g id="node31" class="node" pointer-events="visible" data-name="tdinv">

<ellipse fill="none" stroke="black" cx="1166.5" cy="-378" rx="30.78" ry="18"/>
<text text-anchor="middle" x="1166.5" y="-373.8" font-family="Times,serif" font-size="14.00">tdinv</text>
</g>
<!-- busdcbar&#45;&gt;tdinv -->
<g id="edge31" class="edge" data-name="busdcbar-&gt;tdinv">

<path fill="none" stroke="black" d="M1007.01,-437.9C1038.38,-427.62 1085.86,-411.61 1126.5,-396 1128.3,-395.31 1130.13,-394.59 1131.97,-393.86"/>
<polygon fill="black" stroke="black" points="1133.19,-397.14 1141.1,-390.1 1130.52,-390.67 1133.19,-397.14"/>
</g>
<!-- mp1_inv -->
<g id="node34" class="node" pointer-events="visible" data-name="mp1_inv">

<ellipse fill="none" stroke="black" cx="1260.5" cy="-378" rx="45.25" ry="18"/>
<text text-anchor="middle" x="1260.5" y="-373.8" font-family="Times,serif" font-size="14.00">mp1_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp1_inv -->
<g id="edge35" class="edge" data-name="busdcbar-&gt;mp1_inv">

<path fill="none" stroke="black" d="M1012.5,-440.99C1059.28,-431.37 1138.95,-414.25 1206.5,-396 1209.43,-395.21 1212.43,-394.36 1215.45,-393.49"/>
<polygon fill="black" stroke="black" points="1216.33,-396.88 1224.9,-390.65 1214.32,-390.17 1216.33,-396.88"/>
</g>
<!-- mp2_inv -->
<g id="node37" class="node" pointer-events="visible" data-name="mp2_inv">

<ellipse fill="none" stroke="black" cx="1369.5" cy="-378" rx="45.25" ry="18"/>
<text text-anchor="middle" x="1369.5" y="-373.8" font-family="Times,serif" font-size="14.00">mp2_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp2_inv -->
<g id="edge39" class="edge" data-name="busdcbar-&gt;mp2_inv">

<path fill="none" stroke="black" d="M1016.35,-444.28C1081.8,-436.78 1208.68,-420.42 1314.5,-396 1317.85,-395.23 1321.3,-394.36 1324.74,-393.42"/>
<polygon fill="black" stroke="black" points="1325.57,-396.83 1334.23,-390.72 1323.65,-390.1 1325.57,-396.83"/>
</g>
<!-- mp3_inv -->
<g id="node40" class="node" pointer-events="visible" data-name="mp3_inv">

<ellipse fill="none" stroke="black" cx="903.5" cy="-378" rx="45.25" ry="18"/>
<text text-anchor="middle" x="903.5" y="-373.8" font-family="Times,serif" font-size="14.00">mp3_inv</text>
</g>
<!-- busdcbar&#45;&gt;mp3_inv -->
<g id="edge43" class="edge" data-name="busdcbar-&gt;mp3_inv">

<path fill="none" stroke="black" d="M956.5,-432.76C947.88,-424.02 937.07,-413.05 927.47,-403.31"/>
<polygon fill="black" stroke="black" points="930,-400.89 920.48,-396.23 925.01,-405.8 930,-400.89"/>
</g>
<!-- invertor1 -->
<g id="node43" class="node" pointer-events="visible" data-name="invertor1">

<ellipse fill="none" stroke="black" cx="793.5" cy="-378" rx="46.37" ry="18"/>
<text text-anchor="middle" x="793.5" y="-373.8" font-family="Times,serif" font-size="14.00">invertor1</text>
</g>
<!-- busdcbar&#45;&gt;invertor1 -->
<g id="edge47" class="edge" data-name="busdcbar-&gt;invertor1">

<path fill="none" stroke="black" d="M940.92,-436.65C911.7,-425.22 868.06,-408.15 835.83,-395.55"/>
<polygon fill="black" stroke="black" points="837.38,-392.4 826.79,-392.02 834.83,-398.92 837.38,-392.4"/>
</g>
<!-- invertor2 -->
<g id="node46" class="node" pointer-events="visible" data-name="invertor2">

<ellipse fill="none" stroke="black" cx="682.5" cy="-378" rx="46.37" ry="18"/>
<text text-anchor="middle" x="682.5" y="-373.8" font-family="Times,serif" font-size="14.00">invertor2</text>
</g>
<!-- busdcbar&#45;&gt;invertor2 -->
<g id="edge51" class="edge" data-name="busdcbar-&gt;invertor2">

<path fill="none" stroke="black" d="M932.54,-440.86C885.79,-431.13 806.15,-413.9 738.5,-396 735.22,-395.13 731.84,-394.2 728.45,-393.24"/>
<polygon fill="black" stroke="black" points="729.68,-389.95 719.1,-390.51 727.72,-396.67 729.68,-389.95"/>
</g>
<!-- tdmotor -->
<g id="node32" class="node" pointer-events="visible" data-name="tdmotor">

<ellipse fill="none" stroke="black" cx="1123.5" cy="-306" rx="41.73" ry="18"/>
<text text-anchor="middle" x="1123.5" y="-301.8" font-family="Times,serif" font-size="14.00">tdmotor</text>
</g>
<!-- tdinv&#45;&gt;tdmotor -->
<g id="edge32" class="edge" data-name="tdinv-&gt;tdmotor">

<path fill="none" stroke="black" d="M1156.53,-360.76C1151.5,-352.58 1145.28,-342.45 1139.6,-333.2"/>
<polygon fill="black" stroke="black" points="1142.73,-331.61 1134.51,-324.92 1136.77,-335.28 1142.73,-331.61"/>
</g>
<!-- tdgearbox -->
<g id="node33" class="node" pointer-events="visible" data-name="tdgearbox">

<ellipse fill="none" stroke="black" cx="1123.5" cy="-162" rx="49.8" ry="18"/>
<text text-anchor="middle" x="1123.5" y="-157.8" font-family="Times,serif" font-size="14.00">tdgearbox</text>
</g>
<!-- tdmotor&#45;&gt;tdgearbox -->
<g id="edge33" class="edge" data-name="tdmotor-&gt;tdgearbox">

<path fill="none" stroke="black" d="M1123.5,-287.59C1123.5,-263.61 1123.5,-220.14 1123.5,-191.42"/>
<polygon fill="black" stroke="black" points="1127,-191.62 1123.5,-181.62 1120,-191.62 1127,-191.62"/>
</g>
<!-- tdgearbox&#45;&gt;well1 -->
<g id="edge34" class="edge" data-name="tdgearbox-&gt;well1">

<path fill="none" stroke="black" d="M1123.5,-143.59C1123.5,-119.61 1123.5,-76.14 1123.5,-47.42"/>
<polygon fill="black" stroke="black" points="1127,-47.62 1123.5,-37.62 1120,-47.62 1127,-47.62"/>
</g>
<!-- mp_motor7 -->
<g id="node35" class="node" pointer-events="visible" data-name="mp_motor7">

<ellipse fill="none" stroke="black" cx="1239.5" cy="-306" rx="56.2" ry="18"/>
<text text-anchor="middle" x="1239.5" y="-301.8" font-family="Times,serif" font-size="14.00">mp_motor7</text>
</g>
<!-- mp1_inv&#45;&gt;mp_motor7 -->
<g id="edge36" class="edge" data-name="mp1_inv-&gt;mp_motor7">

<path fill="none" stroke="black" d="M1255.31,-359.7C1253.08,-352.24 1250.4,-343.32 1247.89,-334.97"/>
<polygon fill="black" stroke="black" points="1251.3,-334.13 1245.07,-325.55 1244.59,-336.14 1251.3,-334.13"/>
</g>
<!-- mp1 -->
<g id="node36" class="node" pointer-events="visible" data-name="mp1">

<ellipse fill="none" stroke="black" cx="1190.5" cy="-90" rx="28.41" ry="18"/>
<text text-anchor="middle" x="1190.5" y="-85.8" font-family="Times,serif" font-size="14.00">mp1</text>
</g>
<!-- mp_motor7&#45;&gt;mp1 -->
<g id="edge37" class="edge" data-name="mp_motor7-&gt;mp1">

<path fill="none" stroke="black" d="M1235.58,-287.85C1227.16,-251.07 1207.29,-164.29 1196.97,-119.24"/>
<polygon fill="black" stroke="black" points="1200.43,-118.67 1194.79,-109.71 1193.61,-120.24 1200.43,-118.67"/>
</g>
<!-- mp1&#45;&gt;well1 -->
<g id="edge38" class="edge" data-name="mp1-&gt;well1">

<path fill="none" stroke="black" d="M1176.3,-74.15C1167.53,-65 1156.1,-53.05 1146.11,-42.62"/>
<polygon fill="black" stroke="black" points="1148.65,-40.21 1139.2,-35.4 1143.59,-45.05 1148.65,-40.21"/>
</g>
<!-- mp_motor8 -->
<g id="node38" class="node" pointer-events="visible" data-name="mp_motor8">

<ellipse fill="none" stroke="black" cx="1369.5" cy="-306" rx="56.2" ry="18"/>
<text text-anchor="middle" x="1369.5" y="-301.8" font-family="Times,serif" font-size="14.00">mp_motor8</text>
</g>
<!-- mp2_inv&#45;&gt;mp_motor8 -->
<g id="edge40" class="edge" data-name="mp2_inv-&gt;mp_motor8">

<path fill="none" stroke="black" d="M1369.5,-359.7C1369.5,-352.41 1369.5,-343.73 1369.5,-335.54"/>
<polygon fill="black" stroke="black" points="1373,-335.62 1369.5,-325.62 1366,-335.62 1373,-335.62"/>
</g>
<!-- mp2 -->
<g id="node39" class="node" pointer-events="visible" data-name="mp2">

<ellipse fill="none" stroke="black" cx="1294.5" cy="-162" rx="28.41" ry="18"/>
<text text-anchor="middle" x="1294.5" y="-157.8" font-family="Times,serif" font-size="14.00">mp2</text>
</g>
<!-- mp_motor8&#45;&gt;mp2 -->
<g id="edge41" class="edge" data-name="mp_motor8-&gt;mp2">

<path fill="none" stroke="black" d="M1360.46,-287.87C1347.54,-263.41 1323.68,-218.25 1308.48,-189.46"/>
<polygon fill="black" stroke="black" points="1311.59,-187.86 1303.83,-180.65 1305.4,-191.13 1311.59,-187.86"/>
</g>
<!-- mp2&#45;&gt;well1 -->
<g id="edge42" class="edge" data-name="mp2-&gt;well1">

<path fill="none" stroke="black" d="M1285.43,-144.77C1273.97,-125.45 1252.69,-93.14 1227.5,-72 1207.68,-55.36 1181.75,-42.13 1160.74,-33.05"/>
<polygon fill="black" stroke="black" points="1162.23,-29.88 1151.65,-29.26 1159.54,-36.34 1162.23,-29.88"/>
</g>
<!-- mp_motor9 -->
<g id="node41" class="node" pointer-events="visible" data-name="mp_motor9">

<ellipse fill="none" stroke="black" cx="991.5" cy="-306" rx="56.2" ry="18"/>
<text text-anchor="middle" x="991.5" y="-301.8" font-family="Times,serif" font-size="14.00">mp_motor9</text>
</g>
<!-- mp3_inv&#45;&gt;mp_motor9 -->
<g id="edge44" class="edge" data-name="mp3_inv-&gt;mp_motor9">

<path fill="none" stroke="black" d="M923.03,-361.46C934.53,-352.32 949.28,-340.59 962.15,-330.35"/>
<polygon fill="black" stroke="black" points="964.28,-333.13 969.93,-324.16 959.92,-327.65 964.28,-333.13"/>
</g>
<!-- mp3 -->
<g id="node42" class="node" pointer-events="visible" data-name="mp3">

<ellipse fill="none" stroke="black" cx="1029.5" cy="-90" rx="28.41" ry="18"/>
<text text-anchor="middle" x="1029.5" y="-85.8" font-family="Times,serif" font-size="14.00">mp3</text>
</g>
<!-- mp_motor9&#45;&gt;mp3 -->
<g id="edge45" class="edge" data-name="mp_motor9-&gt;mp3">

<path fill="none" stroke="black" d="M994.55,-287.85C1001.07,-251.14 1016.42,-164.66 1024.44,-119.53"/>
<polygon fill="black" stroke="black" points="1027.88,-120.18 1026.18,-109.72 1020.99,-118.96 1027.88,-120.18"/>
</g>
<!-- mp3&#45;&gt;well1 -->
<g id="edge46" class="edge" data-name="mp3-&gt;well1">

<path fill="none" stroke="black" d="M1047.19,-75.83C1060.75,-65.73 1079.74,-51.59 1095.38,-39.94"/>
<polygon fill="black" stroke="black" points="1097.47,-42.75 1103.4,-33.97 1093.29,-37.14 1097.47,-42.75"/>
</g>
<!-- dw_motor1 -->
<g id="node44" class="node" pointer-events="visible" data-name="dw_motor1">

<ellipse fill="none" stroke="black" cx="828.5" cy="-306" rx="55.63" ry="18"/>
<text text-anchor="middle" x="828.5" y="-301.8" font-family="Times,serif" font-size="14.00">dw_motor1</text>
</g>
<!-- invertor1&#45;&gt;dw_motor1 -->
<g id="edge48" class="edge" data-name="invertor1-&gt;dw_motor1">

<path fill="none" stroke="black" d="M801.98,-360.05C805.83,-352.35 810.49,-343.03 814.82,-334.36"/>
<polygon fill="black" stroke="black" points="817.85,-336.14 819.19,-325.63 811.59,-333.01 817.85,-336.14"/>
</g>
<!-- dw_gearbox1 -->
<g id="node45" class="node" pointer-events="visible" data-name="dw_gearbox1">

<ellipse fill="none" stroke="black" cx="828.5" cy="-234" rx="63.7" ry="18"/>
<text text-anchor="middle" x="828.5" y="-229.8" font-family="Times,serif" font-size="14.00">dw_gearbox1</text>
</g>
<!-- dw_motor1&#45;&gt;dw_gearbox1 -->
<g id="edge49" class="edge" data-name="dw_motor1-&gt;dw_gearbox1">

<path fill="none" stroke="black" d="M828.5,-287.7C828.5,-280.41 828.5,-271.73 828.5,-263.54"/>
<polygon fill="black" stroke="black" points="832,-263.62 828.5,-253.62 825,-263.62 832,-263.62"/>
</g>
<!-- dw_gearbox1&#45;&gt;drum -->
<g id="edge50" class="edge" data-name="dw_gearbox1-&gt;drum">

<path fill="none" stroke="black" d="M797.4,-217.98C773.77,-206.58 741.31,-190.91 717.09,-179.21"/>
<polygon fill="black" stroke="black" points="718.83,-176.17 708.3,-174.97 715.78,-182.47 718.83,-176.17"/>
</g>
<!-- dw_motor2 -->
<g id="node47" class="node" pointer-events="visible" data-name="dw_motor2">

<ellipse fill="none" stroke="black" cx="699.5" cy="-306" rx="55.63" ry="18"/>
<text text-anchor="middle" x="699.5" y="-301.8" font-family="Times,serif" font-size="14.00">dw_motor2</text>
</g>
<!-- invertor2&#45;&gt;dw_motor2 -->
<g id="edge52" class="edge" data-name="invertor2-&gt;dw_motor2">

<path fill="none" stroke="black" d="M686.71,-359.7C688.5,-352.32 690.63,-343.52 692.64,-335.25"/>
<polygon fill="black" stroke="black" points="696.03,-336.12 694.99,-325.58 689.23,-334.47 696.03,-336.12"/>
</g>
<!-- dw_gearbox2 -->
<g id="node48" class="node" pointer-events="visible" data-name="dw_gearbox2">

<ellipse fill="none" stroke="black" cx="683.5" cy="-234" rx="63.7" ry="18"/>
<text text-anchor="middle" x="683.5" y="-229.8" font-family="Times,serif" font-size="14.00">dw_gearbox2</text>
</g>
<!-- dw_motor2&#45;&gt;dw_gearbox2 -->
<!-- dw_motor2&#45;&gt;dw_gearbox2 -->
<g id="edge53" class="edge" data-name="dw_motor2-&gt;dw_gearbox2">

<path fill="none" stroke="black" d="M695.55,-287.7C693.86,-280.32 691.85,-271.52 689.96,-263.25"/>
<polygon fill="black" stroke="black" points="693.39,-262.55 687.75,-253.58 686.57,-264.11 693.39,-262.55"/>
</g>
<!-- dw_gearbox2&#45;&gt;drum -->

<!-- dw_gearbox2&#45;&gt;drum -->
<g id="edge54" class="edge" data-name="dw_gearbox2-&gt;drum">

<path fill="none" stroke="black" d="M683.5,-215.7C683.5,-208.41 683.5,-199.73 683.5,-191.54"/>
<polygon fill="black" stroke="black" points="687,-191.62 683.5,-181.62 680,-191.62 687,-191.62"/>
</g>

</g>
</svg>